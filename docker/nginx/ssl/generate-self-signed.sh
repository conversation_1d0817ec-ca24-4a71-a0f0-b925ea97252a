#!/bin/bash

# Generate self-signed SSL certificate for local development
# For production, use Let's Encrypt or your CA-signed certificates

DOMAIN=${1:-localhost}
DAYS=${2:-365}

echo "Generating self-signed SSL certificate for $DOMAIN..."

# Generate private key
openssl genrsa -out key.pem 2048

# Generate certificate signing request
openssl req -new -key key.pem -out csr.pem -subj "/C=US/ST=State/L=City/O=Organization/CN=$DOMAIN"

# Generate self-signed certificate
openssl x509 -req -days $DAYS -in csr.pem -signkey key.pem -out cert.pem

# Clean up CSR
rm csr.pem

echo "SSL certificate generated successfully!"
echo "  - Certificate: cert.pem"
echo "  - Private Key: key.pem"
echo ""
echo "Note: This is a self-signed certificate for development only."
echo "For production, use Let's Encrypt or a proper CA-signed certificate."