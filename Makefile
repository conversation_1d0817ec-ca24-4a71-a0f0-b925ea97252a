# Production Deployment Makefile
# Run 'make help' to see available commands

.PHONY: help
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Available targets:'
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

.PHONY: setup
setup: ## Initial setup for production deployment
	@echo "Setting up production environment..."
	@cp .env.production.example .env.production
	@echo "Please edit .env.production with your configuration values"
	@chmod +x scripts/*.sh
	@chmod +x docker/nginx/ssl/generate-self-signed.sh
	@echo "Setup complete. Edit .env.production before running 'make deploy'"

.PHONY: ssl-dev
ssl-dev: ## Generate self-signed SSL certificate for development
	@echo "Generating self-signed SSL certificate..."
	@cd docker/nginx/ssl && bash generate-self-signed.sh localhost

.PHONY: build
build: ## Build Docker images
	@echo "Building Docker images..."
	@docker-compose -f docker-compose.production.yml build --no-cache

.PHONY: deploy
deploy: ## Deploy the application in production mode
	@echo "Deploying application..."
	@docker-compose -f docker-compose.production.yml --env-file .env.production up -d
	@echo "Application deployed. Checking status..."
	@sleep 5
	@make status

.PHONY: deploy-with-monitoring
deploy-with-monitoring: ## Deploy with monitoring stack
	@echo "Deploying application with monitoring..."
	@docker-compose -f docker-compose.production.yml -f docker-compose.monitoring.yml --env-file .env.production up -d
	@echo "Application deployed with monitoring."
	@echo "Access points:"
	@echo "  - Application: https://localhost"
	@echo "  - Grafana: http://localhost:3001"
	@echo "  - Prometheus: http://localhost:9090"
	@sleep 5
	@make status

.PHONY: stop
stop: ## Stop all services
	@echo "Stopping all services..."
	@docker-compose -f docker-compose.production.yml -f docker-compose.monitoring.yml down

.PHONY: restart
restart: ## Restart all services
	@echo "Restarting all services..."
	@docker-compose -f docker-compose.production.yml --env-file .env.production restart

.PHONY: status
status: ## Check status of all services
	@echo "Service status:"
	@docker-compose -f docker-compose.production.yml ps
	@echo ""
	@echo "Health checks:"
	@docker ps --format "table {{.Names}}\t{{.Status}}" | grep lockin

.PHONY: logs
logs: ## Show logs from all services
	@docker-compose -f docker-compose.production.yml logs -f

.PHONY: logs-app
logs-app: ## Show application logs only
	@docker-compose -f docker-compose.production.yml logs -f app

.PHONY: logs-db
logs-db: ## Show database logs only
	@docker-compose -f docker-compose.production.yml logs -f postgres

.PHONY: backup
backup: ## Create database backup
	@echo "Creating database backup..."
	@docker-compose -f docker-compose.production.yml --profile backup run --rm backup
	@echo "Backup complete."

.PHONY: restore
restore: ## Restore database from backup
	@echo "Available backups:"
	@docker exec lockin-postgres ls -lh /backups/backup_*.sql.gz 2>/dev/null || echo "No backups found"
	@echo ""
	@read -p "Enter backup filename to restore: " backup_file; \
	docker exec -it lockin-postgres /restore.sh $$backup_file

.PHONY: db-shell
db-shell: ## Access PostgreSQL shell
	@docker exec -it lockin-postgres psql -U ${POSTGRES_USER:-lockin} -d ${POSTGRES_DB:-lockin_db}

.PHONY: app-shell
app-shell: ## Access application container shell
	@docker exec -it lockin-app /bin/sh

.PHONY: redis-cli
redis-cli: ## Access Redis CLI
	@docker exec -it lockin-redis redis-cli

.PHONY: test
test: ## Run tests in container
	@echo "Running tests..."
	@docker-compose -f docker-compose.production.yml exec app npm test

.PHONY: migrate
migrate: ## Run database migrations
	@echo "Running database migrations..."
	@docker-compose -f docker-compose.production.yml exec app npm run db:migrate

.PHONY: seed
seed: ## Seed database with test data
	@echo "Seeding database..."
	@docker-compose -f docker-compose.production.yml exec app npm run db:seed

.PHONY: clean
clean: ## Clean up containers and volumes (WARNING: Deletes all data!)
	@echo "WARNING: This will delete all containers and volumes!"
	@read -p "Are you sure? (yes/no): " confirm; \
	if [ "$$confirm" = "yes" ]; then \
		docker-compose -f docker-compose.production.yml -f docker-compose.monitoring.yml down -v; \
		echo "Cleanup complete."; \
	else \
		echo "Cleanup cancelled."; \
	fi

.PHONY: monitor-cpu
monitor-cpu: ## Monitor CPU usage of containers
	@watch -n 2 'docker stats --no-stream | grep lockin'

.PHONY: health-check
health-check: ## Check health endpoints
	@echo "Checking health endpoints..."
	@curl -f http://localhost/health || echo "App health check failed"
	@echo ""
	@docker exec lockin-postgres pg_isready || echo "Database health check failed"
	@echo ""
	@docker exec lockin-redis redis-cli ping || echo "Redis health check failed"

.PHONY: update
update: ## Update and restart services with new code
	@echo "Updating application..."
	@git pull
	@make build
	@make migrate
	@docker-compose -f docker-compose.production.yml --env-file .env.production up -d
	@echo "Update complete."

.PHONY: scale
scale: ## Scale application instances (usage: make scale NUM=3)
	@docker-compose -f docker-compose.production.yml --env-file .env.production up -d --scale app=$(NUM)

# Default number of instances if not specified
NUM ?= 2