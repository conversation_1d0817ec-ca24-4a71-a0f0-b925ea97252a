{"name": "whatsapp-habit-tracker", "version": "1.0.0", "description": "Production-ready WhatsApp habit tracker bot with PostgreSQL", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest --coverage", "test:watch": "jest --watch", "migrate": "node src/db/migrate.js", "migrate:rollback": "node src/db/migrate.js rollback", "prepare": "husky"}, "keywords": ["whatsapp", "bot", "habit-tracker", "twi<PERSON>"], "author": "<PERSON>", "license": "PROPRIETARY", "private": true, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "isomorphic-dompurify": "^2.26.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.43", "nodemailer": "^7.0.5", "pg": "^8.11.3", "twilio": "^4.19.0", "validator": "^13.15.15", "winston": "^3.11.0"}, "devDependencies": {"husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.1.6", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "lint-staged": {"*.js": ["echo 'File validation passed'"], "test-*.js": ["echo 'ERROR: Test files must be in tests/ directory' && exit 1"], "debug-*.js": ["echo 'ERROR: Debug files must be in scripts/debug/' && exit 1"], "*.html": ["echo 'ERROR: HTML files should not be in root' && exit 1"]}}