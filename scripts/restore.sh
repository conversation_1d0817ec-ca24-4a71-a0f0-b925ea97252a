#!/bin/bash

# PostgreSQL Restore Script
# Run this script to restore a database backup

set -e

# Configuration
BACKUP_DIR="/backups"
DB_HOST="${POSTGRES_HOST:-postgres}"
DB_NAME="${POSTGRES_DB:-lockin_db}"
DB_USER="${POSTGRES_USER:-lockin}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if backup file is provided
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}Available backups:${NC}"
    ls -lh "${BACKUP_DIR}"/backup_*.sql.gz 2>/dev/null || echo "No backups found"
    echo ""
    echo -e "${BLUE}Usage: $0 <backup_file>${NC}"
    echo -e "${BLUE}Example: $0 backup_lockin_db_20250101_120000.sql.gz${NC}"
    exit 1
fi

BACKUP_FILE="${BACKUP_DIR}/$1"

# Check if backup file exists
if [ ! -f "${BACKUP_FILE}" ]; then
    echo -e "${RED}Error: Backup file not found: ${BACKUP_FILE}${NC}"
    exit 1
fi

echo -e "${YELLOW}WARNING: This will restore the database from backup.${NC}"
echo -e "${YELLOW}Current database content will be replaced!${NC}"
echo ""
echo -e "Backup file: ${BACKUP_FILE}"
echo -e "Target database: ${DB_NAME}"
echo ""
read -p "Are you sure you want to continue? (yes/no): " CONFIRM

if [ "${CONFIRM}" != "yes" ]; then
    echo -e "${RED}Restore cancelled.${NC}"
    exit 0
fi

echo -e "${GREEN}Starting database restore...${NC}"

# Create a temporary backup of current database
TEMP_BACKUP="${BACKUP_DIR}/temp_backup_before_restore_$(date +%Y%m%d_%H%M%S).sql.gz"
echo -e "${YELLOW}Creating temporary backup of current database...${NC}"
PGPASSWORD="${POSTGRES_PASSWORD}" pg_dump \
    -h "${DB_HOST}" \
    -U "${DB_USER}" \
    -d "${DB_NAME}" \
    --no-owner \
    --no-acl \
    | gzip > "${TEMP_BACKUP}"

echo -e "${GREEN}Temporary backup created: ${TEMP_BACKUP}${NC}"

# Restore the backup
echo -e "${YELLOW}Restoring database from backup...${NC}"
gunzip -c "${BACKUP_FILE}" | PGPASSWORD="${POSTGRES_PASSWORD}" psql \
    -h "${DB_HOST}" \
    -U "${DB_USER}" \
    -d "${DB_NAME}" \
    -v ON_ERROR_STOP=1

# Check if restore was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Database restored successfully!${NC}"
    echo -e "${GREEN}Temporary backup kept at: ${TEMP_BACKUP}${NC}"
else
    echo -e "${RED}Restore failed!${NC}"
    echo -e "${YELLOW}Attempting to restore from temporary backup...${NC}"
    
    gunzip -c "${TEMP_BACKUP}" | PGPASSWORD="${POSTGRES_PASSWORD}" psql \
        -h "${DB_HOST}" \
        -U "${DB_USER}" \
        -d "${DB_NAME}" \
        -v ON_ERROR_STOP=1
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Database restored to previous state.${NC}"
    else
        echo -e "${RED}Critical error: Could not restore to previous state!${NC}"
        echo -e "${RED}Manual intervention required.${NC}"
    fi
    exit 1
fi