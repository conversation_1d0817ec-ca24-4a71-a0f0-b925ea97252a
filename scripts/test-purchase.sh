#!/bin/bash

# Test ThriveCart webhook with a real purchase simulation
# Usage: ./test-purchase.sh YOUR_EMAIL

EMAIL=${1:-<EMAIL>}
ORDER_ID="ORD-$(date +%s)"

echo "🚀 Simulating ThriveCart purchase for: $EMAIL"
echo "📦 Order ID: $ORDER_ID"
echo ""

curl -X POST http://165.22.17.127:3001/webhook/thrivecart \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "event=order.success&thrivecart_secret=${TEST_WEBHOOK_SECRET:-test-secret-$(date +%s)}&customer_email=$EMAIL&email=$EMAIL&customer_id=CUST-$ORDER_ID&customer_name=Test+Customer&order_id=$ORDER_ID&order_total=5.00&total=500&currency=USD&product_name=Habit+Tracker+Monthly"

echo ""
echo "✅ Webhook sent! Check your email at: $EMAIL"
echo "📧 Email will be from: <EMAIL>"