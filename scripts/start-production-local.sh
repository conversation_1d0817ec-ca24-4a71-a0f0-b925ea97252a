#!/bin/bash

# Production-like Local Environment Startup Script
set -e

echo "Starting Production-like Local Environment..."
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if .env.production exists
if [ ! -f .env.production ]; then
    echo -e "${RED}Error: .env.production file not found!${NC}"
    exit 1
fi

# Function to wait for a service
wait_for_service() {
    local service=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${YELLOW}Waiting for $service on port $port...${NC}"
    
    while ! nc -z localhost $port 2>/dev/null; do
        if [ $attempt -ge $max_attempts ]; then
            echo -e "${RED}Error: $service failed to start after $max_attempts attempts${NC}"
            return 1
        fi
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${GREEN}$service is ready!${NC}"
    return 0
}

# Stop any existing containers
echo "Stopping any existing containers..."
docker-compose -f docker-compose.local-prod.yml down 2>/dev/null || true

# Start PostgreSQL and Redis
echo "Starting PostgreSQL and Redis containers..."
docker-compose -f docker-compose.local-prod.yml up -d

# Wait for PostgreSQL
wait_for_service "PostgreSQL" 5432
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to start PostgreSQL${NC}"
    exit 1
fi

# Wait for Redis
wait_for_service "Redis" 6379
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to start Redis${NC}"
    exit 1
fi

# Wait a bit more for PostgreSQL to be fully ready
sleep 3

# Run database migrations
echo -e "${YELLOW}Running database migrations...${NC}"
NODE_ENV=production node src/db/migrate.js

# Create required directories
echo "Creating required directories..."
mkdir -p logs
mkdir -p uploads
mkdir -p temp

# Set proper permissions
chmod 755 logs uploads temp

# Start the application in production mode
echo -e "${GREEN}=========================================="
echo "Production environment is ready!"
echo "Starting application in production mode..."
echo "==========================================${NC}"

# Export production environment
export NODE_ENV=production

# Load production environment variables
set -a
source .env.production
set +a

# Start the application
exec node src/server.js