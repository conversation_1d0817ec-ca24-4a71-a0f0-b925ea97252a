#!/usr/bin/env node

require('dotenv').config();

async function sendFinalTest() {
  try {
    console.log('📧 SENDING FINAL TEST EMAIL TO YOUR INBOX...');
    
    const thrivecartController = require('../../src/controllers/thrivecartController');
    
    // Final test purchase - this will send an actual email
    const webhookPayload = {
      thrivecart_account: 'richvieren',
      event: 'order.success',
      event_id: `FINAL_TEST_${Date.now()}`,
      
      customer_email: '<EMAIL>', // Your test email
      customer_name: 'Final Test Customer',
      customer_first_name: 'Final',
      customer_last_name: 'Test',
      
      product_name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
      order_total: '3999',
      payment_plan_name: 'Annual subscription (ongoing) ($39.99)',
      
      webhook_charges: [{
        name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
        amount: '3999',
        payment_plan_name: 'Annual subscription (ongoing) ($39.99)'
      }],
      
      thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now()
    };
    
    const req = { body: webhookPayload };
    const res = {
      status: (code) => ({
        json: (data) => {
          console.log(`✅ Webhook processed: ${code}`);
          return res;
        }
      })
    };
    
    console.log('⚡ Processing webhook to send email...');
    
    await thrivecartController.handleWebhook(req, res);
    
    console.log('\n📬 EMAIL SENT!');
    console.log('Check your inbox: <EMAIL>');
    console.log('Subject: "Your LOCK IN Access Code Is Here"');
    console.log('Template: Simple HTML (2,109 characters)');
    console.log('');
    console.log('🎯 This email should have:');
    console.log('  ✅ Complete content (no truncation)');
    console.log('  ✅ Access code');
    console.log('  ✅ Setup instructions');
    console.log('  ✅ Rich\'s signature');
    console.log('  ✅ Social links (@richvieren, @vieren)');
    console.log('  ✅ Affiliate program info');
    console.log('  ✅ Footer links and copyright');
    console.log('');
    console.log('📧 The email is now BASIC AS FUCK - no fancy CSS to break!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

sendFinalTest().catch(console.error);