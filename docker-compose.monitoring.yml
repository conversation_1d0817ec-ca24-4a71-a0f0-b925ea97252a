version: '3.8'

# Monitoring stack overlay for production
# Use with: docker-compose -f docker-compose.production.yml -f docker-compose.monitoring.yml up

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: lockin-prometheus
    restart: unless-stopped
    volumes:
      - ./docker/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    networks:
      - app-network

  grafana:
    image: grafana/grafana:latest
    container_name: lockin-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_INSTALL_PLUGINS: grafana-piechart-panel
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3001:3000"
    networks:
      - app-network
    depends_on:
      - prometheus

  node-exporter:
    image: prom/node-exporter:latest
    container_name: lockin-node-exporter
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - app-network

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: lockin-postgres-exporter
    restart: unless-stopped
    environment:
      DATA_SOURCE_NAME: postgresql://${POSTGRES_USER:-lockin}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-lockin_db}?sslmode=disable
    ports:
      - "9187:9187"
    networks:
      - app-network
    depends_on:
      - postgres

  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: lockin-redis-exporter
    restart: unless-stopped
    environment:
      REDIS_ADDR: redis://redis:6379
    ports:
      - "9121:9121"
    networks:
      - app-network
    depends_on:
      - redis

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: lockin-cadvisor
    restart: unless-stopped
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    networks:
      - app-network
    privileged: true
    devices:
      - /dev/kmsg

volumes:
  prometheus-data:
    driver: local
  grafana-data:
    driver: local