version: '3.8'

services:
  postgres-prod:
    image: postgres:15-alpine
    container_name: habittracker-postgres-prod
    environment:
      POSTGRES_DB: habittracker_prod
      POSTGRES_USER: habituser
      POSTGRES_PASSWORD: prodpassword123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=en_US.utf8 --lc-ctype=en_US.utf8"
    ports:
      - "5432:5432"
    volumes:
      - postgres-prod-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U habituser -d habittracker_prod"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - habittracker-prod

  redis-prod:
    image: redis:7-alpine
    container_name: habittracker-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis-prod-data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - habittracker-prod

volumes:
  postgres-prod-data:
    name: habittracker-postgres-prod-data
  redis-prod-data:
    name: habittracker-redis-prod-data

networks:
  habittracker-prod:
    name: habittracker-prod-network
    driver: bridge