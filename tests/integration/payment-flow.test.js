#!/usr/bin/env node

/**
 * ThriveCart Payment Flow Test Script
 * Tests the complete payment-to-bot-access pipeline
 */

const axios = require('axios');
const { Pool } = require('pg');

const BASE_URL = 'http://localhost:3001';
const TEST_EMAIL = '<EMAIL>';
const TEST_PHONE = '+1234567890';

// Database connection
const pool = new Pool({
  user: 'postgres',
  password: 'postgres',
  host: 'localhost',
  database: 'lockin',
  port: 5432
});

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testPaymentFlow() {
  console.log('🚀 Starting ThriveCart Payment Flow Test\n');
  
  try {
    // Step 1: Check test mode status
    console.log('1️⃣ Checking test mode status...');
    const statusResponse = await axios.get(`${BASE_URL}/test/status`);
    console.log('✅ Test mode active:', statusResponse.data.testMode);
    console.log('   Initial stats:', statusResponse.data.stats);
    
    // Step 2: Clear any existing test data
    console.log('\n2️⃣ Clearing existing test data...');
    await axios.delete(`${BASE_URL}/test/clear-data`);
    console.log('✅ Test data cleared');
    
    // Step 3: Simulate ThriveCart webhook
    console.log('\n3️⃣ Simulating ThriveCart webhook for monthly subscription...');
    const thrivecartPayload = {
      event: 'order.success',
      customer: {
        email: TEST_EMAIL,
        name: 'Test User'
      },
      order: {
        order_id: 'TEST-ORDER-' + Date.now(),
        product_id: 'habit-tracker-monthly',
        amount: 5.00,
        currency: 'USD'
      }
    };
    
    const webhookResponse = await axios.post(
      `${BASE_URL}/webhook/thrivecart`,
      thrivecartPayload
    );
    console.log('✅ Webhook processed:', webhookResponse.data);
    
    // Step 4: Verify payment was recorded
    console.log('\n4️⃣ Verifying payment record...');
    const paymentQuery = await pool.query(
      'SELECT * FROM paid_users WHERE email = $1 ORDER BY created_at DESC LIMIT 1',
      [TEST_EMAIL]
    );
    
    if (paymentQuery.rows.length === 0) {
      throw new Error('Payment not recorded in database');
    }
    
    const payment = paymentQuery.rows[0];
    console.log('✅ Payment recorded:');
    console.log('   - Access Code:', payment.access_code);
    console.log('   - Subscription Type:', payment.subscription_type);
    console.log('   - Status:', payment.status);
    
    // Step 5: Check access code
    console.log('\n5️⃣ Verifying access code...');
    const accessCodeQuery = await pool.query(
      'SELECT * FROM access_codes WHERE code = $1',
      [payment.access_code]
    );
    
    if (accessCodeQuery.rows.length === 0) {
      throw new Error('Access code not created');
    }
    
    console.log('✅ Access code active:', accessCodeQuery.rows[0].is_active);
    
    // Step 6: Check email queue
    console.log('\n6️⃣ Checking email queue...');
    const emailQuery = await pool.query(
      'SELECT * FROM email_queue WHERE to_email = $1 ORDER BY created_at DESC LIMIT 1',
      [TEST_EMAIL]
    );
    
    if (emailQuery.rows.length > 0) {
      console.log('✅ Email queued:');
      console.log('   - Subject:', emailQuery.rows[0].subject);
      console.log('   - Template:', emailQuery.rows[0].template);
      console.log('   - Status:', emailQuery.rows[0].status);
    } else {
      console.log('⚠️ No email queued (might have been sent already)');
    }
    
    // Step 7: Test access code validation
    console.log('\n7️⃣ Testing access code validation...');
    const validationData = {
      phoneNumber: TEST_PHONE,
      accessCode: payment.access_code
    };
    
    const validationResponse = await axios.post(
      `${BASE_URL}/test/validate-access-code`,
      validationData
    );
    console.log('✅ Access code valid:', validationResponse.data.valid);
    
    // Step 8: Simulate yearly subscription (to test affiliate code)
    console.log('\n8️⃣ Simulating yearly subscription for affiliate features...');
    const yearlyPayload = {
      event: 'order.success',
      customer: {
        email: '<EMAIL>',
        name: 'Yearly User'
      },
      order: {
        order_id: 'TEST-YEARLY-' + Date.now(),
        product_id: 'habit-tracker-yearly',
        amount: 30.00,
        currency: 'USD'
      }
    };
    
    await axios.post(`${BASE_URL}/webhook/thrivecart`, yearlyPayload);
    
    const yearlyQuery = await pool.query(
      'SELECT * FROM paid_users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (yearlyQuery.rows.length > 0) {
      const yearlyPayment = yearlyQuery.rows[0];
      console.log('✅ Yearly subscription created:');
      console.log('   - Affiliate Code:', yearlyPayment.affiliate_code);
      console.log('   - Is Affiliate:', yearlyPayment.is_affiliate);
    }
    
    // Final status check
    console.log('\n9️⃣ Final status check...');
    const finalStatus = await axios.get(`${BASE_URL}/test/status`);
    console.log('✅ Final stats:', finalStatus.data.stats);
    
    console.log('\n✅ Payment flow test completed successfully!\n');
    
    // Production setup reminder
    console.log('📝 Production Setup Reminder:');
    console.log('1. Configure your ThriveCart webhook to point to: https://your-domain.com/webhook/thrivecart');
    console.log('2. Set PAYMENT_TEST_MODE=false in production');
    console.log('3. Add your ThriveCart secret to .env');
    console.log('4. Configure email service (SMTP) settings');
    console.log('5. Set up SSL certificate for HTTPS');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the test
testPaymentFlow();