#!/usr/bin/env node

const axios = require('axios');
const querystring = require('querystring');

async function testWebhook() {
  try {
    // Prepare the webhook data in the format ThriveCart sends
    const webhookData = {
      event: 'order.success',
      thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
      'customer[email]': '<EMAIL>',
      'customer[customer_id]': 'CUST-NEW-001',
      'customer[name]': 'New Test Customer',
      'order[order_id]': 'ORD-NEW-001',
      'order[total]': '5.00',
      'order[currency]': 'USD',
      'product[product_name]': 'Habit Tracker Monthly'
    };

    // Convert to URL-encoded format
    const formData = querystring.stringify(webhookData);

    console.log('🚀 Testing ThriveCart webhook with proper format...\n');
    console.log('📤 Sending webhook data...');
    
    const response = await axios.post(
      'http://localhost:3001/webhook/thrivecart',
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    console.log('✅ Webhook processed successfully!');
    console.log('Response:', response.data);
    
    // Check if email was sent
    console.log('\n📧 Email should have been sent to: <EMAIL>');
    console.log('   From: <EMAIL>');
    
  } catch (error) {
    console.error('❌ Webhook failed:', error.response?.data || error.message);
  }
}

testWebhook();