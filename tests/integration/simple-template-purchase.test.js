#!/usr/bin/env node

require('dotenv').config();

async function testSimpleTemplatePurchase() {
  try {
    console.log('🎯 TESTING SIMPLE TEMPLATE WITH REAL PURCHASE FLOW...');
    
    const thrivecartController = require('./src/controllers/thrivecartController');
    
    // Simulate regular annual purchase
    const webhookPayload = {
      thrivecart_account: 'richvieren',
      event: 'order.success',
      event_id: `SIMPLE_TEST_${Date.now()}`,
      
      customer_email: '<EMAIL>',
      customer_name: 'Simple Test Customer',
      customer_first_name: 'Simple',
      customer_last_name: 'Test',
      
      product_name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
      order_total: '3999',
      payment_plan_name: 'Annual subscription (ongoing) ($39.99)',
      
      webhook_charges: [{
        name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
        amount: '3999',
        payment_plan_name: 'Annual subscription (ongoing) ($39.99)'
      }],
      
      thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now()
    };
    
    const req = { body: webhookPayload };
    const res = {
      status: (code) => ({
        json: (data) => {
          console.log(`Response ${code}:`, JSON.stringify(data, null, 2));
          return res;
        }
      })
    };
    
    console.log('⚡ PROCESSING PURCHASE WITH SIMPLE TEMPLATE...');
    
    await thrivecartController.handleWebhook(req, res);
    
    // Check the email in the queue
    const pool = require('./src/db/connection');
    const result = await pool.query(`
      SELECT id, to_email, template, template_data, status, created_at
      FROM email_queue 
      WHERE to_email = '<EMAIL>'
      ORDER BY id DESC LIMIT 1
    `);
    
    if (result.rows.length > 0) {
      const emailRecord = result.rows[0];
      console.log('\n📧 EMAIL QUEUED:');
      console.log('ID:', emailRecord.id);
      console.log('Template:', emailRecord.template);
      console.log('Status:', emailRecord.status);
      
      // Generate the content that would be sent
      const emailService = require('./src/services/emailService');
      const templateFunc = emailService.templates[emailRecord.template];
      const templateData = typeof emailRecord.template_data === 'string' 
        ? JSON.parse(emailRecord.template_data) 
        : emailRecord.template_data;
      
      const { subject, html, text } = templateFunc(templateData);
      
      console.log('\n📊 SIMPLE TEMPLATE OUTPUT:');
      console.log('Subject:', subject);
      console.log('HTML length:', html.length);
      console.log('Text length:', text.length);
      
      // Save the actual production output
      require('fs').writeFileSync('/var/www/lockin/simple-production-email.html', html);
      console.log('\n💾 Saved production email to: simple-production-email.html');
      
      console.log('\n🎉 SUCCESS! Simple template is working in production flow.');
      console.log('📏 Email is only', html.length, 'characters - much smaller than before!');
      
    } else {
      console.log('❌ No email found in queue');
    }
    
    await pool.end();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testSimpleTemplatePurchase().catch(console.error);