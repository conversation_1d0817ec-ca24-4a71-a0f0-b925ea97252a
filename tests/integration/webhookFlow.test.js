const request = require('supertest');
const app = require('../../src/app');
const User = require('../../src/models/User');
const complianceService = require('../../src/services/complianceService');
const logger = require('../../src/config/logger');

jest.mock('../../src/models/User');
jest.mock('../../src/services/complianceService');
jest.mock('../../src/config/logger');

describe('Webhook Integration Flow', () => {
  let mockReq, mockRes;

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock app to use test database or in-memory
    // For true integration, this would use test DB setup
    process.env.NODE_ENV = 'test';
  });

  describe('Basic Message Flow', () => {
    it('should handle new user onboarding flow', async () => {
      // Mock user creation
      User.findOrCreate.mockResolvedValue({
        id: 1,
        phone: '+**********',
        current_state: 'ONBOARDING',
        status: 'LOCKED'
      });
      complianceService.handleComplianceFlow.mockResolvedValue({
        message: 'Welcome! Type your age.',
        newState: 'AGE_VERIFICATION'
      });

      const mockReq = {
        body: {
          Body: 'START',
          From: '+**********'
        }
      };

      const mockRes = {
        type: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis()
      };

      // Simulate webhook call
      const event = {
        type: 'whatsapp.inbound',
        data: mockReq.body
      };

      // Mock the app's route handling
      // This is simplified - in real integration test, use supertest against running app
      const webhookHandler = require('../../src/controllers/webhookController');
      await webhookHandler.handleIncomingMessage(mockReq, mockRes);

      expect(User.findOrCreate).toHaveBeenCalledWith('+**********');
      expect(complianceService.handleComplianceFlow).toHaveBeenCalled();
      expect(mockRes.type).toHaveBeenCalledWith('text/xml');
      expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('Welcome! Type your age.'));
    });

    it('should handle habit logging flow', async () => {
      const mockUser = {
        id: 1,
        phone: '+**********',
        current_state: 'MAIN_MENU',
        status: 'ACTIVE'
      };
      User.findByPhone.mockResolvedValue(mockUser);
      // Mock state machine or handler to process 'LOG HABIT 1 YES'
      // This would involve Habit.logHabit call, but for integration, test the flow

      const mockReq = {
        body: {
          Body: 'LOG HABIT 1 YES',
          From: '+**********'
        }
      };

      const mockRes = {
        type: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis()
      };

      const event = {
        type: 'whatsapp.inbound',
        data: mockReq.body
      };

      const webhookHandler = require('../../src/controllers/webhookController');
      await webhookHandler.handleIncomingMessage(mockReq, mockRes);

      expect(User.findByPhone).toHaveBeenCalledWith('+**********');
      // Additional assertions for habit logging would go here
      expect(mockRes.type).toHaveBeenCalledWith('text/xml');
      expect(mockRes.send).toHaveBeenCalled();
    });

    it('should handle compliance opt-out', async () => {
      const mockUser = {
        id: 1,
        phone: '+**********',
        current_state: 'MAIN_MENU',
        status: 'ACTIVE'
      };
      User.findByPhone.mockResolvedValue(mockUser);
      // Mock compliance middleware to catch STOP
      // For integration test, this would be part of the full chain

      const mockReq = {
        body: {
          Body: 'STOP',
          From: '+**********'
        }
      };

      const mockRes = {
        type: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis()
      };

      const complianceMiddleware = require('../../src/middleware/compliance');
      await complianceMiddleware.checkStopKeywords(mockReq, mockRes, jest.fn());

      expect(User.findByPhone).toHaveBeenCalledWith('+**********');
      expect(mockRes.type).toHaveBeenCalledWith('text/xml');
      expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('You have been unsubscribed'));
    });

    it('should validate webhook input', async () => {
      const mockReq = {
        body: {
          Body: 'test',
          From: '+**********',
          To: '+**********',
          MessageSid: 'sid123',
          AccountSid: 'AC123'
        }
      };

      const mockRes = {
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      const mockNext = jest.fn();

      const { validateWebhook } = require('../../src/middleware/validation');
      await validateWebhook(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      // Body should be sanitized if needed
    });

    it('should reject invalid webhook', async () => {
      const mockReq = {
        body: {
          // Missing required fields
        }
      };

      const mockRes = {
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      const mockNext = jest.fn();

      const { validateWebhook } = require('../../src/middleware/validation');
      await validateWebhook(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.send).toHaveBeenCalledWith('Invalid request');
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle webhook processing error', async () => {
      const mockReq = {
        body: {
          Body: 'ERROR TEST',
          From: '+**********'
        }
      };

      const mockRes = {
        type: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis()
      };

      // Mock handler to throw
      const webhookHandler = require('../../src/controllers/webhookController');
      const originalHandle = webhookHandler.handleIncomingMessage;
      webhookHandler.handleIncomingMessage = jest.fn().mockRejectedValue(new Error('Test error'));

      await webhookHandler.handleIncomingMessage(mockReq, mockRes);

      expect(mockRes.type).toHaveBeenCalledWith('text/xml');
      expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('Sorry, I encountered an error'));
      expect(logger.error).toHaveBeenCalledWith('Webhook processing error', expect.any(Object));

      // Restore original
      webhookHandler.handleIncomingMessage = originalHandle;
    });
  });
});