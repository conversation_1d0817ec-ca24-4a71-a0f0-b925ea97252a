#!/usr/bin/env node

require('dotenv').config();
const express = require('express');
const thrivecartController = require('./src/controllers/thrivecartController');
const pool = require('./src/db/connection');
const emailService = require('./src/services/emailService');

async function testExactProductionFlow() {
  try {
    console.log('🎯 TESTING EXACT PRODUCTION WEBHOOK -> EMAIL FLOW...');
    
    // Clear any existing test emails first
    await pool.query("DELETE FROM email_queue WHERE to_email LIKE '<EMAIL>'");
    
    // Create the exact webhook request that ThriveCart would send
    const webhookPayload = {
      thrivecart_account: process.env.THRIVECART_ACCOUNT || 'richvieren',
      event: 'order.success',
      event_id: `TEST_PROD_${Date.now()}`,
      
      // Customer data
      customer_email: '<EMAIL>',
      customer_name: 'Production Test Customer',
      customer_first_name: 'Production',
      customer_last_name: 'Customer',
      
      // Annual subscription (the broken one)
      product_name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
      order_total: '3999', // ThriveCart sends in cents
      payment_plan_name: 'Annual subscription (ongoing) ($39.99)',
      product_permalink: 'lock-in-annual',
      
      // Webhook charges that determine template selection
      webhook_charges: [{
        name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
        amount: '3999',
        payment_plan_name: 'Annual subscription (ongoing) ($39.99)'
      }],
      
      // Additional data
      currency: 'USD',
      affiliate_id: 'AFF-TESTPROD',
      transaction_id: `TXN_PROD_${Date.now()}`,
      subscription_id: `SUB_PROD_${Date.now()}`,
      order_id: `ORDER_PROD_${Date.now()}`,
      created_at: new Date().toISOString(),
      test_mode: 'true',  // But simulate production flow
      thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now() // Test webhook secret
    };
    
    console.log('\n📦 WEBHOOK PAYLOAD:');
    console.log('Event:', webhookPayload.event);
    console.log('Product:', webhookPayload.product_name);
    console.log('Amount:', webhookPayload.order_total);
    console.log('Payment Plan:', webhookPayload.payment_plan_name);
    
    // Create mock Express request exactly like production
    const req = {
      body: webhookPayload,
      headers: {
        'content-type': 'application/json',
        'user-agent': 'ThriveCart/1.0'
      }
    };
    
    const res = {
      status: (code) => ({
        json: (data) => {
          console.log(`\n📨 RESPONSE ${code}:`, JSON.stringify(data, null, 2));
          return res;
        }
      })
    };
    
    console.log('\n⚡ PROCESSING WEBHOOK (exact production flow)...');
    
    // Process through exact same controller as production
    await thrivecartController.handleWebhook(req, res);
    
    console.log('\n📧 CHECKING EMAIL QUEUE...');
    
    // Check if email was queued
    const queueResult = await pool.query(`
      SELECT id, to_email, template, template_data, status, created_at 
      FROM email_queue 
      WHERE to_email = '<EMAIL>'
      ORDER BY id DESC LIMIT 1
    `);
    
    if (!queueResult.rows.length) {
      console.log('❌ NO EMAIL QUEUED!');
      return;
    }
    
    const emailRecord = queueResult.rows[0];
    console.log('✅ Email queued with ID:', emailRecord.id);
    console.log('Template:', emailRecord.template);
    console.log('Status:', emailRecord.status);
    
    // Generate content exactly like production would
    const templateFunc = emailService.templates[emailRecord.template];
    const templateData = typeof emailRecord.template_data === 'string' 
      ? JSON.parse(emailRecord.template_data) 
      : emailRecord.template_data;
    
    console.log('\n📊 TEMPLATE DATA:');
    console.log(JSON.stringify(templateData, null, 2));
    
    const { subject, html, text } = templateFunc(templateData);
    
    console.log('\n📈 GENERATED EMAIL CONTENT:');
    console.log('Subject:', subject);
    console.log('HTML length:', html.length);
    console.log('Text length:', text.length);
    
    // Check footer content in generated HTML
    const step5Index = html.indexOf('Start building unstoppable habits!');
    if (step5Index !== -1) {
      const afterStep5 = html.substring(step5Index + 'Start building unstoppable habits!'.length);
      console.log('Characters after step 5:', afterStep5.length);
      
      const footerChecks = [
        { name: 'Rich signature', check: afterStep5.includes("Let's Lock In") && afterStep5.includes('Rich') },
        { name: 'Affiliate section', check: afterStep5.includes('💰 Earn with Lock In') },
        { name: 'Social links', check: afterStep5.includes('@richvieren') },
        { name: 'Copyright', check: afterStep5.includes('© 2025 Lock In') }
      ];
      
      console.log('\n🔍 FOOTER CONTENT CHECK:');
      footerChecks.forEach(({ name, check }) => {
        console.log(`  ${check ? '✅' : '❌'} ${name}`);
      });
      
      const allFooterGood = footerChecks.every(({ check }) => check);
      console.log(`\n${allFooterGood ? '🎉 TEMPLATE GENERATES COMPLETE CONTENT!' : '❌ TEMPLATE CONTENT IS BROKEN!'}`);
    }
    
    // Now trigger the queue processor exactly like production does
    console.log('\n⚡ PROCESSING EMAIL QUEUE (production style)...');
    
    await emailService.processQueue();
    
    // Check email status after processing
    const processedResult = await pool.query(`
      SELECT status, sent_at, error_message 
      FROM email_queue 
      WHERE id = $1
    `, [emailRecord.id]);
    
    const processed = processedResult.rows[0];
    console.log('\n📬 EMAIL PROCESSING RESULT:');
    console.log('Status:', processed.status);
    console.log('Sent at:', processed.sent_at);
    if (processed.error_message) {
      console.log('❌ Error:', processed.error_message);
    }
    
    // Save the exact generated content
    require('fs').writeFileSync('/var/www/lockin/production-flow-test.html', html);
    console.log('\n💾 Saved generated content to: production-flow-test.html');
    
    console.log('\n🎯 CONCLUSION:');
    if (processed.status === 'sent') {
      console.log('✅ Production flow works - email sent successfully');
      console.log('✅ Template generates complete content');
      console.log('🔍 If customer emails are truncated but this test works,');
      console.log('    then issue is in SMTP provider delivery or client rendering');
    } else {
      console.log('❌ Production flow failed - email not sent');
    }
    
  } catch (error) {
    console.error('❌ Error in production flow test:', error.message);
    console.error(error.stack);
  }
  
  await pool.end();
}

testExactProductionFlow().catch(console.error);