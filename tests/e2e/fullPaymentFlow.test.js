const request = require('supertest');
const app = require('../../src/app');
const pool = require('../../src/db/connection');
const { resetTestData } = require('../helpers/clearTestUserLogs'); // Assume helper exists

jest.mock('../../src/db/connection');
jest.mock('../../src/services/emailService');
jest.mock('crypto');

// Mock the app to use test database
process.env.NODE_ENV = 'test';

describe('Full Payment Flow E2E', () => {
  let server;

  beforeAll(() => {
    server = app.listen(0); // Start on random port
  });

  afterAll(() => {
    server.close();
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    await resetTestData(); // Clear test data between tests
  });

  it('should process ThriveCart webhook to email delivery', async () => {
    const mockPayload = {
      event: 'order.success',
      customer: {
        email: '<EMAIL>'
      },
      order: {
        order_id: 'ORDER123',
        total: 500,
        charges: [{ name: 'Monthly Subscription', amount: 500 }]
      }
    };

    const response = await request(server)
      .post('/api/thrivecart/webhook')
      .send(mockPayload)
      .expect(200);

    expect(response.body).toEqual({ received: true });

    // Verify DB changes
    const userResult = await pool.query('SELECT * FROM paid_users WHERE email = $1', ['<EMAIL>']);
    expect(userResult.rows.length).toBeGreaterThan(0);

    // Verify email queued
    const emailResult = await pool.query('SELECT * FROM email_queue WHERE to_email = $1', ['<EMAIL>']);
    expect(emailResult.rows.length).toBeGreaterThan(0);
    expect(emailResult.rows[0].template).toBe('welcome_monthly');

    // Verify email processed
    const emailService = require('../../src/services/emailService');
    expect(emailService.processQueue).toHaveBeenCalled();
  });


  it('should handle subscription renewal flow', async () => {
    // Setup existing user with subscription
    await pool.query(
      `INSERT INTO paid_users (id, email, subscription_type, status, expires_at, test_mode)
       VALUES (999, '<EMAIL>', 'monthly', 'active', '2024-01-10', true)`,
      []
    );

    const mockPayload = {
      event: 'order.rebill_success',
      customer: { email: '<EMAIL>' },
      order: { order_id: 'RENEW123', total: 500 }
    };

    const response = await request(server)
      .post('/api/thrivecart/webhook')
      .send(mockPayload)
      .expect(200);

    expect(response.body).toEqual({ received: true });

    // Verify subscription extended
    const updatedUser = await pool.query('SELECT * FROM paid_users WHERE email = $1', ['<EMAIL>']);
    expect(updatedUser.rows[0].expires_at).toBe('2024-02-10T00:00:00Z'); // Extended by 30 days
  });

  it('should handle refund flow', async () => {
    // Setup user with active subscription
    await pool.query(
      `INSERT INTO paid_users (id, email, status, subscription_id, test_mode)
       VALUES (998, '<EMAIL>', 'active', 'SUB789', true)`,
      []
    );

    const mockPayload = {
      event: 'order.refund',
      customer: { email: '<EMAIL>' },
      order: { order_id: 'REFUND456', total: 500 }
    };

    const response = await request(server)
      .post('/api/thrivecart/webhook')
      .send(mockPayload)
      .expect(200);

    expect(response.body).toEqual({ received: true });

    // Verify status updated to expired
    const updatedUser = await pool.query('SELECT status FROM paid_users WHERE email = $1', ['<EMAIL>']);
    expect(updatedUser.rows[0].status).toBe('expired');

    // Verify refund transaction logged
    const transactionResult = await pool.query('SELECT type FROM payment_transactions WHERE paid_user_id = 998');
    expect(transactionResult.rows[0].type).toBe('refund');
  });

  it('should handle compliance during onboarding', async () => {
    // Mock user needs compliance
    User.findOrCreate.mockResolvedValue({
      id: 1,
      phone: '+1234567890',
      current_state: 'ONBOARDING',
      status: 'LOCKED',
      age_verified: false
    });
    ComplianceService.prototype.handleComplianceFlow.mockResolvedValue({
      message: 'Age verification required.',
      newState: 'AGE_VERIFICATION'
    });

    const mockReq = {
      body: {
        Body: 'START',
        From: '+1234567890'
      }
    };

    const mockRes = {
      type: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };

    const webhookHandler = require('../../src/controllers/webhookController');
    await webhookHandler.handleIncomingMessage(mockReq, mockRes);

    expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('Age verification required.'));
  });
});