const MotivationalQuotes = require('../../../src/utils/motivationalQuotes');

describe('MotivationalQuotes', () => {
  let quotes;

  beforeEach(() => {
    // Re-import to get fresh instance
    jest.resetModules();
    quotes = require('../../../src/utils/motivationalQuotes');
  });

  describe('getRandomQuote', () => {
    it('should return a quote from specified category', () => {
      // Mock Math.random to control selection
      const originalRandom = Math.random;
      let callCount = 0;
      Math.random = jest.fn(() => {
        callCount++;
        return callCount === 1 ? 0 : 0.5; // First call index 0, second index ~half
      });

      const quote1 = quotes.getRandomQuote('streak');
      const quote2 = quotes.getRandomQuote('streak');

      Math.random = originalRandom;

      // Verify from streak array (index 0 and ~5)
      expect(quote1).toBe("Every streak starts with day one.");
      expect(quote2).toBe("The magic happens when you don't give up.");
      expect(quote1).not.toBe(quote2);
    });

    it('should fallback to general category for invalid category', () => {
      const originalRandom = Math.random;
      Math.random = jest.fn(() => 0); // First quote

      const quote = quotes.getRandomQuote('invalid');

      Math.random = originalRandom;

      expect(quote).toBe("Progress is progress, no matter how small.");
    });

    it('should return different quotes on multiple calls', () => {
      const originalRandom = Math.random;
      let index = 0;
      Math.random = jest.fn(() => {
        const i = index++;
        return i / 10; // Sequential indices
      });

      const quotesArray = [];
      for (let i = 0; i < 3; i++) {
        quotesArray.push(quotes.getRandomQuote('general'));
      }

      Math.random = originalRandom;

      // Should get first 3 general quotes
      expect(quotesArray[0]).toBe("Progress is progress, no matter how small.");
      expect(quotesArray[1]).toBe("Every day is a new opportunity to improve.");
      expect(quotesArray[2]).toBe("Consistency is the mother of mastery.");
      // All unique
      expect(new Set(quotesArray).size).toBe(3);
    });
  });

  describe('getQuoteByContext', () => {
    it('should map context to appropriate category quote', () => {
      const originalRandom = Math.random;
      Math.random = jest.fn(() => 0); // First quote in category

      expect(quotes.getQuoteByContext('perfect_day')).toBe("Perfect days are made, not found.");
      expect(quotes.getQuoteByContext('long_streak')).toBe("Every streak starts with day one.");
      expect(quotes.getQuoteByContext('recovery')).toBe("Comeback stories are the best stories.");
      expect(quotes.getQuoteByContext('milestone_30')).toBe("30 days of dedication - incredible!");
      expect(quotes.getQuoteByContext('mastery')).toBe("Mastery is a journey, not a destination.");

      Math.random = originalRandom;
    });

    it('should handle milestone_100 same as milestone_30', () => {
      const originalRandom = Math.random;
      Math.random = jest.fn(() => 0);

      const quote30 = quotes.getQuoteByContext('milestone_30');
      const quote100 = quotes.getQuoteByContext('milestone_100');

      Math.random = originalRandom;

      expect(quote30).toBe(quote100);
    });

    it('should fallback to general for unknown context', () => {
      const originalRandom = Math.random;
      Math.random = jest.fn(() => 0);

      const quote = quotes.getQuoteByContext('unknown');

      Math.random = originalRandom;

      expect(quote).toBe("Progress is progress, no matter how small.");
    });

    it('should return different quotes for same context on multiple calls', () => {
      const originalRandom = Math.random;
      let index = 0;
      Math.random = jest.fn(() => {
        const i = index++;
        return i / 10;
      });

      const quotesArray = [];
      for (let i = 0; i < 3; i++) {
        quotesArray.push(quotes.getQuoteByContext('long_streak'));
      }

      Math.random = originalRandom;

      // First 3 streak quotes
      expect(quotesArray[0]).toBe("Every streak starts with day one.");
      expect(quotesArray[1]).toBe("Streaks are built one day at a time.");
      expect(quotesArray[2]).toBe("Your streak is your superpower.");
      expect(new Set(quotesArray).size).toBe(3);
    });
  });

  describe('instance', () => {
    it('should be a singleton instance', () => {
      const quotes1 = require('../../../src/utils/motivationalQuotes');
      const quotes2 = require('../../../src/utils/motivationalQuotes');

      expect(quotes1).toBe(quotes2);
    });

    it('should have all quote categories defined', () => {
      expect(quotes.quotes).toBeDefined();
      expect(quotes.quotes.general).toHaveLength(10);
      expect(quotes.quotes.streak).toHaveLength(10);
      expect(quotes.quotes.perfectDay).toHaveLength(10);
      expect(quotes.quotes.recovery).toHaveLength(10);
      expect(quotes.quotes.milestone).toHaveLength(10);
      expect(quotes.quotes.mastery).toHaveLength(10);
    });
  });
});