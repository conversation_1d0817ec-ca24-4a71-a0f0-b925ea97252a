const {
  hashPI<PERSON>,
  hashPhone,
  hashEmail,
  hashName,
  hashIP,
  maskPhone,
  maskEmail,
  createSafeLogObject,
  createAuditLog,
  verifyPIIHash
} = require('../../../src/utils/piiHasher');
const logger = require('../../../src/config/logger');
const crypto = require('crypto');

jest.mock('../../../src/config/logger');

describe('piiHasher', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set consistent salt for predictable tests
    process.env.PII_SALT = 'test-salt-123';
  });

  describe('hashPII', () => {
    it('should hash data successfully with type prefix', () => {
      const result = hashPII('testdata', 'phone');
      expect(result).toMatch(/^phone_[a-f0-9]{8}$/);
      // Consistent hash for same input
      expect(hashPII('testdata', 'phone')).toBe(result);
    });

    it('should handle empty/null data', () => {
      expect(hashPII('')).toBe('unknown_unknown');
      expect(hashPII(null)).toBe('unknown_unknown');
      expect(hashPII(undefined)).toBe('unknown_unknown');
    });

    it('should handle hashing error gracefully', () => {
      jest.spyOn(crypto, 'createHash').mockImplementation(() => {
        throw new Error('Hash error');
      });
      jest.spyOn(logger, 'error');

      const result = hashPII('testdata', 'phone');
      expect(result).toBe('phone_error');
      expect(logger.error).toHaveBeenCalledWith('Error hashing PII', { error: 'Hash error' });
    });
  });

  describe('hashPhone', () => {
    it('should hash phone and clean whatsapp prefix', () => {
      const result = hashPhone('whatsapp:+1234567890');
      expect(result).toMatch(/^phone_[a-f0-9]{8}$/);
      // Same without prefix
      expect(hashPhone('+1234567890')).toBe(result);
    });

    it('should handle empty phone', () => {
      expect(hashPhone('')).toBe('phone_unknown');
      expect(hashPhone(null)).toBe('phone_unknown');
    });
  });

  describe('hashEmail', () => {
    it('should hash normalized email (lowercase, trimmed)', () => {
      const result = hashEmail(' <EMAIL> ');
      expect(result).toMatch(/^email_[a-f0-9]{8}$/);
      // Consistent normalization
      expect(hashEmail('<EMAIL>')).toBe(result);
    });

    it('should handle empty email', () => {
      expect(hashEmail('')).toBe('email_unknown');
    });
  });

  describe('hashName', () => {
    it('should hash normalized name (lowercase, trimmed)', () => {
      const result = hashName(' John Doe ');
      expect(result).toMatch(/^name_[a-f0-9]{8}$/);
      expect(hashName('john doe')).toBe(result);
    });

    it('should handle empty name', () => {
      expect(hashName('')).toBe('name_unknown');
    });
  });

  describe('hashIP', () => {
    it('should hash IP address', () => {
      const result = hashIP('***********');
      expect(result).toMatch(/^ip_[a-f0-9]{8}$/);
      expect(hashIP('***********')).toBe(result);
    });

    it('should handle empty IP', () => {
      expect(hashIP('')).toBe('ip_unknown');
    });
  });

  describe('maskPhone', () => {
    it('should mask phone showing last 4 digits', () => {
      expect(maskPhone('+1234567890')).toBe('***7890');
      expect(maskPhone('whatsapp:+1234567890')).toBe('***7890');
    });

    it('should handle short phone', () => {
      expect(maskPhone('123')).toBe('****');
      expect(maskPhone('')).toBe('****');
    });
  });

  describe('maskEmail', () => {
    it('should mask email appropriately', () => {
      expect(maskEmail('<EMAIL>')).toBe('t***@exa***.***');
      expect(maskEmail('<EMAIL>')).toBe('a***@b.***');
    });

    it('should handle invalid email', () => {
      expect(maskEmail('invalid')).toBe('***@***.***');
      expect(maskEmail('')).toBe('***@***.***');
    });
  });

  describe('createSafeLogObject', () => {
    it('should hash PII fields and keep non-PII', () => {
      const input = {
        phone: '+1234567890',
        email: '<EMAIL>',
        name: 'John Doe',
        ip: '***********',
        age: 30,
        password: 'secret',
        nested: { phone: '+0987654321', safe: 'data' }
      };

      const result = createSafeLogObject(input);

      // Check hashed/masked fields
      expect(result.phone).toMatch(/^phone_[a-f0-9]{8}$/);
      expect(result.phone_masked).toBe('***4321');
      expect(result.email).toMatch(/^email_[a-f0-9]{8}$/);
      expect(result.name).toMatch(/^name_[a-f0-9]{8}$/);
      expect(result.ip).toMatch(/^ip_[a-f0-9]{8}$/);
      expect(result.password).toBe('[REDACTED]');
      expect(result.age).toBe(30); // Non-PII unchanged

      // Check nested recursion
      expect(result.nested.phone).toMatch(/^phone_[a-f0-9]{8}$/);
      expect(result.nested.safe).toBe('data');
    });

    it('should handle non-object input', () => {
      expect(createSafeLogObject('string')).toBe('string');
      expect(createSafeLogObject(123)).toBe(123);
      expect(createSafeLogObject(null)).toBe(null);
    });

    it('should redact sensitive keys', () => {
      const input = { api_key: 'secret123', token: 'abc' };
      const result = createSafeLogObject(input);
      expect(result.api_key).toBe('[REDACTED]');
      expect(result.token).toBe('[REDACTED]');
    });
  });

  describe('createAuditLog', () => {
    it('should create audit log with safe data and requestId', () => {
      const input = { action: 'LOGIN', data: { phone: '+1234567890' } };
      const result = createAuditLog('LOGIN', { phone: '+1234567890', requestId: 'req-123' });

      expect(result.timestamp).toBeDefined();
      expect(result.action).toBe('LOGIN');
      expect(result.data.phone).toMatch(/^phone_[a-f0-9]{8}$/);
      expect(result.requestId).toBe('req-123');
    });

    it('should generate random requestId if not provided', () => {
      const result = createAuditLog('LOGIN');
      expect(result.requestId).toHaveLength(16); // 8 bytes hex
      expect(result.requestId).toMatch(/^[a-f0-9]{16}$/);
    });
  });

  describe('verifyPIIHash', () => {
    it('should verify matching hash', () => {
      const original = '<EMAIL>';
      const hashed = hashEmail(original);
      expect(verifyPIIHash(hashed, original, 'email')).toBe(true);
    });

    it('should reject non-matching hash', () => {
      const hashed = hashEmail('<EMAIL>');
      expect(verifyPIIHash(hashed, '<EMAIL>', 'email')).toBe(false);
    });

    it('should use default type if not specified', () => {
      const original = '+1234567890';
      const hashed = hashPII(original, 'unknown');
      expect(verifyPIIHash(hashed, original)).toBe(true);
    });
  });
});