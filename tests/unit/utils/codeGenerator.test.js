jest.mock('../../../src/db/connection');
const pool = require('../../../src/db/connection');
const {
  generateAccessCode,
  generateAffiliateCode,
  validateAccessCode,
  validateAffiliateCode
} = require('../../../src/utils/codeGenerator');

describe('codeGenerator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateAccessCode', () => {
    it('should generate unique access code successfully on first attempt', async () => {
      // Mock crypto.randomBytes to return predictable value
      const mockRandomBytes = Buffer.from('aabbccddeeff', 'hex');
      jest.spyOn(require('crypto'), 'randomBytes').mockReturnValue(mockRandomBytes);

      // Mock pool.query to return no existing code
      pool.query.mockResolvedValue({ rows: [] });

      const code = await generateAccessCode();

      expect(code).toBe('HABIT-QRVM3E');
      expect(pool.query).toHaveBeenCalledWith(
        'SELECT id FROM paid_users WHERE access_code = $1',
        ['HABIT-QRVM3E']
      );
      expect(require('crypto').randomBytes).toHaveBeenCalledWith(6);
    });

    it('should retry on collision and succeed on second attempt', async () => {
      const mockRandomBytes1 = Buffer.from('aabbccddeeff', 'hex'); // First collision
      const mockRandomBytes2 = Buffer.from('112233445566', 'hex'); // Second success

      jest.spyOn(require('crypto'), 'randomBytes')
        .mockReturnValueOnce(mockRandomBytes1)
        .mockReturnValueOnce(mockRandomBytes2);

      // First query finds existing code (collision)
      pool.query
        .mockResolvedValueOnce({ rows: [{ id: 1 }] }) // Collision
        .mockResolvedValueOnce({ rows: [] }); // Success

      const consoleSpy = jest.spyOn(console, 'log');
      const code = await generateAccessCode();

      expect(code).toBe('HABIT-ESIZRF');
      expect(pool.query).toHaveBeenCalledTimes(2);
      expect(consoleSpy).toHaveBeenCalledWith('Access code collision detected, generating new code');
      consoleSpy.mockRestore();
    });

    it('should throw error after max attempts on collisions', async () => {
      // Mock all attempts to collide
      jest.spyOn(require('crypto'), 'randomBytes').mockReturnValue(Buffer.from('aabbccddeeff', 'hex'));
      pool.query.mockResolvedValue({ rows: [{ id: 1 }] }); // Always collision

      const consoleSpy = jest.spyOn(console, 'log');
      await expect(generateAccessCode()).rejects.toThrow('Failed to generate unique access code after 10 attempts');
      expect(pool.query).toHaveBeenCalledTimes(10);
      expect(consoleSpy).toHaveBeenCalledTimes(10);
      consoleSpy.mockRestore();
    });
  });

  describe('generateAffiliateCode', () => {
    it('should generate unique affiliate code successfully', async () => {
      const mockRandomBytes = Buffer.from('aabbccddeeff', 'hex');
      jest.spyOn(require('crypto'), 'randomBytes').mockReturnValue(mockRandomBytes);
      pool.query.mockResolvedValue({ rows: [] });

      const code = await generateAffiliateCode();

      expect(code).toBe('AFF-QRVM3E');
      expect(pool.query).toHaveBeenCalledWith(
        'SELECT id FROM paid_users WHERE affiliate_code = $1',
        ['AFF-QRVM3E']
      );
    });

    it('should retry on collision and succeed', async () => {
      const mockRandomBytes1 = Buffer.from('aabbccddeeff', 'hex');
      const mockRandomBytes2 = Buffer.from('112233445566', 'hex');

      jest.spyOn(require('crypto'), 'randomBytes')
        .mockReturnValueOnce(mockRandomBytes1)
        .mockReturnValueOnce(mockRandomBytes2);

      pool.query
        .mockResolvedValueOnce({ rows: [{ id: 1 }] }) // Collision
        .mockResolvedValueOnce({ rows: [] }); // Success

      const consoleSpy = jest.spyOn(console, 'log');
      const code = await generateAffiliateCode();

      expect(code).toBe('AFF-ESIZRF');
      expect(consoleSpy).toHaveBeenCalledWith('Affiliate code collision detected, generating new code');
      consoleSpy.mockRestore();
    });

    it('should throw error after max attempts', async () => {
      jest.spyOn(require('crypto'), 'randomBytes').mockReturnValue(Buffer.from('aabbccddeeff', 'hex'));
      pool.query.mockResolvedValue({ rows: [{ id: 1 }] });

      const consoleSpy = jest.spyOn(console, 'log');
      await expect(generateAffiliateCode()).rejects.toThrow('Failed to generate unique affiliate code after 10 attempts');
      expect(consoleSpy).toHaveBeenCalledTimes(10);
      consoleSpy.mockRestore();
    });
  });

  describe('validateAccessCode', () => {
    it('should validate correct access code format', () => {
      expect(validateAccessCode('HABIT-ABC123')).toBe(true);
      expect(validateAccessCode('habit-ABC123')).toBe(true); // Case insensitive
      expect(validateAccessCode('HABIT-ABCDEF')).toBe(true);
    });

    it('should reject invalid access code formats', () => {
      expect(validateAccessCode('HABIT-ABCD')).toBe(false); // Too short
      expect(validateAccessCode('HABIT-ABC1234')).toBe(false); // Too long
      expect(validateAccessCode('HABIT-AB C12')).toBe(false); // Space
      expect(validateAccessCode('AFF-ABC123')).toBe(false); // Wrong prefix
      expect(validateAccessCode('HABITABC123')).toBe(false); // No dash
      expect(validateAccessCode('')).toBe(false); // Empty
      expect(validateAccessCode(null)).toBe(false); // Null
      expect(validateAccessCode(123)).toBe(false); // Number
      expect(validateAccessCode('HABIT-AB#123')).toBe(false); // Special char
    });
  });

  describe('validateAffiliateCode', () => {
    it('should validate correct affiliate code format', () => {
      expect(validateAffiliateCode('AFF-ABC123')).toBe(true);
      expect(validateAffiliateCode('aff-ABC123')).toBe(true); // Case insensitive
      expect(validateAffiliateCode('AFF-ABCDEF')).toBe(true);
    });

    it('should reject invalid affiliate code formats', () => {
      expect(validateAffiliateCode('AFF-ABCD')).toBe(false); // Too short
      expect(validateAffiliateCode('AFF-ABC1234')).toBe(false); // Too long
      expect(validateAffiliateCode('AFF-AB C12')).toBe(false); // Space
      expect(validateAffiliateCode('HABIT-ABC123')).toBe(false); // Wrong prefix
      expect(validateAffiliateCode('AFFABC123')).toBe(false); // No dash
      expect(validateAffiliateCode('')).toBe(false); // Empty
      expect(validateAffiliateCode(null)).toBe(false); // Null
      expect(validateAffiliateCode(123)).toBe(false); // Number
      expect(validateAffiliateCode('AFF-AB#123')).toBe(false); // Special char
    });
  });
});