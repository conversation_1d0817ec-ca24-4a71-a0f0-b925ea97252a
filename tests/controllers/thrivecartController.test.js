const ThriveCartController = require('../../src/controllers/thrivecartController');
const logger = require('../../src/config/logger');
const { products, bumps } = require('../../src/controllers/thrivecart/productConfig');
const { parseBumpOrders, generateCodesWithBumps } = require('../../src/controllers/thrivecart/codeGeneration');
const { 
  identifyProduct,
  handleOrderSuccess,
  handleSubscriptionPayment,
  handleRefund,
  handleSubscriptionCancelled,
  handleSubscriptionResumed,
  logWebhookEvent
} = require('../../src/controllers/thrivecart/webhookHandlers');
const crypto = require('crypto');

jest.mock('../../src/config/logger');
jest.mock('../../src/controllers/thrivecart/productConfig');
jest.mock('../../src/controllers/thrivecart/codeGeneration');
jest.mock('../../src/controllers/thrivecart/webhookHandlers');
jest.mock('crypto');

describe('ThriveCartController', () => {
  let controller;
  let mockReq, mockRes;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
    controller = require('../../src/controllers/thrivecartController');
    
    // Mock req/res
    mockReq = {
      body: {},
      headers: {}
    };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      end: jest.fn()
    };
    
    // Mock env
    process.env.PAYMENT_TEST_MODE = 'true';
    process.env.THRIVECART_WEBHOOK_SECRET = 'test-secret';
  });

  describe('handleWebhook', () => {
    it('should process order.success event', async () => {
      mockReq.body = { event: 'order.success', customer: { email: '<EMAIL>' } };
      logWebhookEvent.mockResolvedValue();
      handleOrderSuccess.mockResolvedValue();

      await controller.handleWebhook(mockReq, mockRes);

      expect(logWebhookEvent).toHaveBeenCalledWith(mockReq.body, mockReq.headers, true);
      expect(controller.verifyThriveCartSecret).toHaveBeenCalledWith(mockReq.body, mockReq.headers);
      expect(handleOrderSuccess).toHaveBeenCalledWith(mockReq.body, true);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ received: true });
    });

    it('should process subscription_payment event', async () => {
      mockReq.body = { event: 'order.subscription_payment' };
      logWebhookEvent.mockResolvedValue();
      handleSubscriptionPayment.mockResolvedValue();

      await controller.handleWebhook(mockReq, mockRes);

      expect(handleSubscriptionPayment).toHaveBeenCalledWith(mockReq.body);
    });

    it('should process refund event', async () => {
      mockReq.body = { event: 'order.refund' };
      logWebhookEvent.mockResolvedValue();
      handleRefund.mockResolvedValue();

      await controller.handleWebhook(mockReq, mockRes);

      expect(handleRefund).toHaveBeenCalledWith(mockReq.body);
    });

    it('should process subscription_cancelled event', async () => {
      mockReq.body = { event: 'order.subscription_cancelled' };
      logWebhookEvent.mockResolvedValue();
      handleSubscriptionCancelled.mockResolvedValue();

      await controller.handleWebhook(mockReq, mockRes);

      expect(handleSubscriptionCancelled).toHaveBeenCalledWith(mockReq.body);
    });

    it('should process subscription_resumed event', async () => {
      mockReq.body = { event: 'order.subscription_resumed' };
      logWebhookEvent.mockResolvedValue();
      handleSubscriptionResumed.mockResolvedValue();

      await controller.handleWebhook(mockReq, mockRes);

      expect(handleSubscriptionResumed).toHaveBeenCalledWith(mockReq.body);
    });

    it('should log unhandled event', async () => {
      mockReq.body = { event: 'unknown.event' };
      logWebhookEvent.mockResolvedValue();

      await controller.handleWebhook(mockReq, mockRes);

      expect(logger.info).toHaveBeenCalledWith('Unhandled ThriveCart event type', { eventType: 'unknown.event' });
      expect(mockRes.status).toHaveBeenCalledWith(200);
    });

    it('should return 401 on verification failure', async () => {
      mockReq.body = { event: 'order.success' };
      logWebhookEvent.mockResolvedValue();
      controller.verifyThriveCartSecret.mockReturnValue(false);

      await controller.handleWebhook(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Invalid secret' });
      expect(logger.error).toHaveBeenCalledWith('Invalid or missing ThriveCart secret/signature');
    });

    it('should return 500 on processing error', async () => {
      mockReq.body = { event: 'order.success' };
      logWebhookEvent.mockResolvedValue();
      controller.verifyThriveCartSecret.mockReturnValue(true);
      handleOrderSuccess.mockRejectedValue(new Error('Handler error'));

      await controller.handleWebhook(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Webhook processing failed' });
      expect(logger.error).toHaveBeenCalledWith('ThriveCart webhook error', expect.any(Object));
    });
  });

  describe('verifyThriveCartSecret', () => {
    it('should verify with header signature', () => {
      mockReq.body = { data: 'test' };
      mockReq.headers['x-thrivecart-signature'] = 'expected-sig';
      crypto.createHmac.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('expected-sig')
      });
      crypto.timingSafeEqual.mockReturnValue(true);

      const result = controller.verifyThriveCartSecret(mockReq.body, mockReq.headers);

      expect(crypto.createHmac).toHaveBeenCalledWith('sha256', 'test-secret');
      expect(crypto.timingSafeEqual).toHaveBeenCalledWith(
        Buffer.from('expected-sig'),
        Buffer.from('expected-sig')
      );
      expect(result).toBe(true);
    });

    it('should verify with payload secret param', () => {
      mockReq.body = { thrivecart_secret: 'test-secret' };
      delete mockReq.headers['x-thrivecart-signature'];
      crypto.timingSafeEqual.mockReturnValue(true);

      const result = controller.verifyThriveCartSecret(mockReq.body, mockReq.headers);

      expect(crypto.timingSafeEqual).toHaveBeenCalledWith(
        Buffer.from('test-secret'),
        Buffer.from('test-secret')
      );
      expect(result).toBe(true);
    });

    it('should fail without secret or signature', () => {
      mockReq.body = { data: 'test' };
      delete mockReq.headers['x-thrivecart-signature'];
      delete mockReq.body.thrivecart_secret;

      const result = controller.verifyThriveCartSecret(mockReq.body, mockReq.headers);

      expect(logger.error).toHaveBeenCalledWith('No ThriveCart signature or secret provided');
      expect(result).toBe(false);
    });

    it('should fail invalid signature', () => {
      mockReq.body = { data: 'test' };
      mockReq.headers['x-thrivecart-signature'] = 'invalid-sig';
      crypto.createHmac.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('expected-sig')
      });
      crypto.timingSafeEqual.mockReturnValue(false);

      const result = controller.verifyThriveCartSecret(mockReq.body, mockReq.headers);

      expect(result).toBe(false);
    });

    it('should fail without configured secret', () => {
      process.env.THRIVECART_WEBHOOK_SECRET = undefined;
      mockReq.body = { data: 'test' };
      mockReq.headers['x-thrivecart-signature'] = 'sig';

      const result = controller.verifyThriveCartSecret(mockReq.body, mockReq.headers);

      expect(logger.error).toHaveBeenCalledWith('ThriveCart webhook secret not configured');
      expect(result).toBe(false);
    });

    it('should handle signature comparison error', () => {
      mockReq.body = { data: 'test' };
      mockReq.headers['x-thrivecart-signature'] = 'sig';
      crypto.createHmac.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValue('expected')
      });
      crypto.timingSafeEqual.mockImplementation(() => { throw new Error('Compare error'); });

      const result = controller.verifyThriveCartSecret(mockReq.body, mockReq.headers);

      expect(logger.error).toHaveBeenCalledWith('Signature comparison failed', expect.any(Object));
      expect(result).toBe(false);
    });
  });

  describe('testWebhook', () => {
    beforeEach(() => {
      process.env.PAYMENT_TEST_MODE = 'true';
    });

    it('should process test webhook in test mode', async () => {
      mockReq.body = { email: '<EMAIL>', productType: 'monthly', includeBumps: ['friend'] };
      jest.spyOn(controller, 'handleWebhook');
      logWebhookEvent.mockResolvedValue();
      identifyProduct.mockReturnValue('monthly');

      await controller.testWebhook(mockReq, mockRes);

      expect(controller.handleWebhook).toHaveBeenCalledWith(mockReq, mockRes);
    });

    it('should return 403 if test mode disabled', async () => {
      process.env.PAYMENT_TEST_MODE = 'false';

      await controller.testWebhook(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Test mode not enabled' });
    });

    it('should build test payload with product and bumps', async () => {
      mockReq.body = { email: '<EMAIL>', productType: 'yearly', includeBumps: ['tribe', 'friend'] };
      jest.spyOn(controller, 'handleWebhook');
      
      // Mock products/bumps
      products.yearly = { name: 'Yearly', price: 30 };
      bumps.tribe = { name: 'Tribe', price: 10 };
      bumps.friend = { name: 'Friend', price: 5 };

      await controller.testWebhook(mockReq, mockRes);

      // Verify payload was built and handleWebhook called
      expect(controller.handleWebhook).toHaveBeenCalled();
    });

    it('should handle test error', async () => {
      mockReq.body = { email: '<EMAIL>' };
      jest.spyOn(controller, 'handleWebhook');
      controller.handleWebhook.mockRejectedValue(new Error('Test error'));

      await controller.testWebhook(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Test error' });
      expect(logger.error).toHaveBeenCalledWith('Test webhook error', expect.any(Object));
    });
  });
});