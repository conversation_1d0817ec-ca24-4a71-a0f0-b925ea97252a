const AuditLog = require('../../src/models/AuditLog');
const pool = require('../../src/db/connection');
const logger = require('../../src/config/logger');
const sqlIntervalBuilder = require('../../src/utils/sqlIntervalBuilder');

jest.mock('../../src/db/connection');
jest.mock('../../src/config/logger');
jest.mock('../../src/utils/sqlIntervalBuilder');

describe('AuditLog', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('log', () => {
    it('should log event successfully with event data', async () => {
      const mockQuery = jest.fn().mockResolvedValue({ rows: [] });
      pool.query.mockImplementation(mockQuery);

      await AuditLog.log('user123', 'LOGIN_SUCCESS', { someData: 'value', phone: '1234567890' });

      expect(pool.query).toHaveBeenCalledWith(
        'INSERT INTO audit_logs (user_id, event_type, event_data, timestamp) VALUES ($1, $2, $3, NOW())',
        ['user123', 'LOGIN_SUCCESS', JSON.stringify({ someData: 'value' })]
      );
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should log event successfully without event data', async () => {
      const mockQuery = jest.fn().mockResolvedValue({ rows: [] });
      pool.query.mockImplementation(mockQuery);

      await AuditLog.log('user123', 'LOGOUT');

      expect(pool.query).toHaveBeenCalledWith(
        'INSERT INTO audit_logs (user_id, event_type, event_data, timestamp) VALUES ($1, $2, $3, NOW())',
        ['user123', 'LOGOUT', JSON.stringify({})]
      );
    });

    it('should silently handle query error', async () => {
      const mockQuery = jest.fn().mockRejectedValue(new Error('DB error'));
      pool.query.mockImplementation(mockQuery);

      await AuditLog.log('user123', 'LOGIN_SUCCESS', { someData: 'value' });

      expect(pool.query).toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith('Error creating audit log', { error: 'DB error' });
    });
  });

  describe('getByUser', () => {
    it('should return audit logs for user successfully', async () => {
      const mockLogs = [{ id: 1, event_type: 'LOGIN' }];
      const mockQuery = jest.fn().mockResolvedValue({ rows: mockLogs });
      pool.query.mockImplementation(mockQuery);

      const result = await AuditLog.getByUser('user123', 50);

      expect(pool.query).toHaveBeenCalledWith(
        'SELECT * FROM audit_logs WHERE user_id = $1 ORDER BY timestamp DESC LIMIT $2',
        ['user123', 50]
      );
      expect(result).toEqual(mockLogs);
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should return empty array if no logs', async () => {
      const mockQuery = jest.fn().mockResolvedValue({ rows: [] });
      pool.query.mockImplementation(mockQuery);

      const result = await AuditLog.getByUser('user123', 100);

      expect(result).toEqual([]);
    });

    it('should throw error on query failure', async () => {
      const mockQuery = jest.fn().mockRejectedValue(new Error('DB error'));
      pool.query.mockImplementation(mockQuery);

      await expect(AuditLog.getByUser('user123')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error getting audit logs', { error: 'DB error' });
    });
  });

  describe('cleanup', () => {
    it('should cleanup old logs successfully', async () => {
      const mockInterval = { query: 'INTERVAL \'90 days\'', value: 90 };
      sqlIntervalBuilder.buildMakeInterval.mockReturnValue(mockInterval);

      const mockQuery = jest.fn().mockResolvedValue({ rowCount: 5 });
      pool.query.mockImplementation(mockQuery);

      const result = await AuditLog.cleanup(90);

      expect(sqlIntervalBuilder.buildMakeInterval).toHaveBeenCalledWith(90, 'days');
      expect(pool.query).toHaveBeenCalledWith(
        `DELETE FROM audit_logs WHERE timestamp < NOW() - ${mockInterval.query} RETURNING id`,
        [mockInterval.value]
      );
      expect(logger.info).toHaveBeenCalledWith('Cleaned up 5 old audit logs');
      expect(result).toBe(5);
    });

    it('should use default 90 days if not specified', async () => {
      const mockInterval = { query: 'INTERVAL \'90 days\'', value: 90 };
      sqlIntervalBuilder.buildMakeInterval.mockReturnValue(mockInterval);
      const mockQuery = jest.fn().mockResolvedValue({ rowCount: 0 });
      pool.query.mockImplementation(mockQuery);

      await AuditLog.cleanup();

      expect(sqlIntervalBuilder.buildMakeInterval).toHaveBeenCalledWith(90, 'days');
    });

    it('should throw error on query failure', async () => {
      const mockInterval = { query: 'INTERVAL \'90 days\'', value: 90 };
      sqlIntervalBuilder.buildMakeInterval.mockReturnValue(mockInterval);
      const mockQuery = jest.fn().mockRejectedValue(new Error('DB error'));
      pool.query.mockImplementation(mockQuery);

      await expect(AuditLog.cleanup()).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error cleaning up audit logs', { error: 'DB error' });
    });
  });
});