const Habit = require('../../src/models/Habit');
const pool = require('../../src/db/connection');
const logger = require('../../src/config/logger');

jest.mock('../../src/db/connection');
jest.mock('../../src/config/logger');

describe('Habit', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('findByUserId', () => {
    it('should return habits for user successfully', async () => {
      const mockHabits = [{ id: 1, habit_number: 1, habit_name: 'Exercise' }];
      pool.query.mockResolvedValue({ rows: mockHabits });

      const result = await Habit.findByUserId('user123');

      expect(pool.query).toHaveBeenCalledWith(
        'SELECT * FROM habits WHERE user_id = $1 ORDER BY habit_number',
        ['user123']
      );
      expect(result).toEqual(mockHabits);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      await expect(Habit.findByUserId('user123')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error finding habits by user', { error: 'DB error' });
    });
  });

  describe('findByUserAndNumber', () => {
    it('should return habit by user and number', async () => {
      const mockHabit = { id: 1, habit_name: 'Exercise' };
      pool.query.mockResolvedValue({ rows: [mockHabit] });

      const result = await Habit.findByUserAndNumber('user123', 1);

      expect(pool.query).toHaveBeenCalledWith(
        'SELECT * FROM habits WHERE user_id = $1 AND habit_number = $2',
        ['user123', 1]
      );
      expect(result).toEqual(mockHabit);
    });

    it('should return null if no habit found', async () => {
      pool.query.mockResolvedValue({ rows: [] });

      const result = await Habit.findByUserAndNumber('user123', 1);

      expect(result).toBeNull();
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      await expect(Habit.findByUserAndNumber('user123', 1)).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error finding habit by number', { error: 'DB error' });
    });
  });

  describe('upsert', () => {
    it('should upsert habit successfully', async () => {
      const mockHabit = { id: 1, habit_name: 'Exercise' };
      pool.query.mockResolvedValue({ rows: [mockHabit] });

      const result = await Habit.upsert('user123', 1, 'Exercise');

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO habits (user_id, habit_number, habit_name, created_at, updated_at)'),
        ['user123', 1, 'Exercise']
      );
      expect(result).toEqual(mockHabit);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      await expect(Habit.upsert('user123', 1, 'Exercise')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error upserting habit', { error: 'DB error' });
    });
  });

  describe('delete', () => {
    it('should delete habit successfully', async () => {
      const mockHabit = { id: 1, habit_name: 'Exercise' };
      pool.query.mockResolvedValue({ rows: [mockHabit] });

      const result = await Habit.delete('user123', 1);

      expect(pool.query).toHaveBeenCalledWith(
        'DELETE FROM habits WHERE user_id = $1 AND habit_number = $2 RETURNING *',
        ['user123', 1]
      );
      expect(result).toEqual(mockHabit);
    });

    it('should return null if no habit deleted', async () => {
      pool.query.mockResolvedValue({ rows: [] });

      const result = await Habit.delete('user123', 1);

      expect(result).toBeNull();
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      await expect(Habit.delete('user123', 1)).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error deleting habit', { error: 'DB error' });
    });
  });

  describe('logHabit', () => {
    it('should log habit completion successfully', async () => {
      const mockLog = { id: 1, completed: true };
      pool.query.mockResolvedValue({ rows: [mockLog] });

      const result = await Habit.logHabit('user123', 1, '2023-01-01', true);

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO habit_logs (user_id, habit_id, log_date, completed, logged_at)'),
        ['user123', 1, '2023-01-01', true]
      );
      expect(result).toEqual(mockLog);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      await expect(Habit.logHabit('user123', 1, '2023-01-01', true)).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error logging habit', { error: 'DB error' });
    });
  });

  describe('getTodayLogs', () => {
    it('should return today logs with timezone', async () => {
      const mockLogs = [{ habit_number: 1, completed: true }];
      pool.query.mockResolvedValue({ rows: mockLogs });

      const result = await Habit.getTodayLogs('user123', 'America/New_York');

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT h.*, hl.completed as completed, hl.logged_at FROM habits h'),
        ['user123', 'America/New_York']
      );
      expect(result).toEqual(mockLogs);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      await expect(Habit.getTodayLogs('user123')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error getting today logs', { error: 'DB error' });
    });
  });

  describe('getProgress', () => {
    it('should return progress for 7 days', async () => {
      const mockProgress = [{ habit_number: 1, completion_rate: 80 }];
      pool.query.mockResolvedValue({ rows: mockProgress });

      const result = await Habit.getProgress('user123', 7, 'UTC');

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('WITH date_series AS (SELECT generate_series'),
        ['user123', 'UTC']
      );
      expect(result).toEqual(mockProgress);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      await expect(Habit.getProgress('user123')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error getting progress', { error: 'DB error' });
    });
  });

  describe('getStreak', () => {
    it('should return current streak for habit', async () => {
      pool.query.mockResolvedValue({ rows: [{ current_streak: 5 }] });

      const result = await Habit.getStreak('user123', 1, 'UTC');

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('WITH RECURSIVE streak_calc AS ('),
        ['user123', 1, 'UTC']
      );
      expect(result).toBe(5);
    });

    it('should return 0 if no streak', async () => {
      pool.query.mockResolvedValue({ rows: [{ current_streak: null }] });

      const result = await Habit.getStreak('user123', 1);

      expect(result).toBe(0);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      await expect(Habit.getStreak('user123', 1)).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error getting streak', { error: 'DB error' });
    });
  });

  // Additional tests for other methods would follow similar patterns
  // For brevity, testing core CRUD and a few analytics methods
  // Full suite would include all 15 methods with comprehensive cases

  describe('getAllLogs', () => {
    it('should return all habit logs', async () => {
      const mockLogs = [{ habit_number: 1, completed: true, log_date: '2023-01-01' }];
      pool.query.mockResolvedValue({ rows: mockLogs });

      const result = await Habit.getAllLogs('user123');

      expect(pool.query).toHaveBeenCalledWith(
        'SELECT h.habit_number, h.habit_name, hl.completed, hl.log_date FROM habits h LEFT JOIN habit_logs hl ON h.id = hl.habit_id WHERE h.user_id = $1 ORDER BY h.habit_number, hl.log_date',
        ['user123']
      );
      expect(result).toEqual(mockLogs);
    });

    it('should return empty array on error', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      const result = await Habit.getAllLogs('user123');

      expect(result).toEqual([]);
      expect(logger.error).toHaveBeenCalledWith('Error getting all logs', { error: 'DB error' });
    });
  });

  describe('getCurrentStreak', () => {
    it('should return current streak across all habits', async () => {
      pool.query.mockResolvedValue({ rows: [{ current_streak: 3 }] });

      const result = await Habit.getCurrentStreak('user123', 'UTC');

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('WITH daily_completions AS ('),
        ['user123', 'UTC']
      );
      expect(result).toBe(3);
    });

    it('should return 0 on error', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));

      const result = await Habit.getCurrentStreak('user123');

      expect(result).toBe(0);
      expect(logger.error).toHaveBeenCalledWith('Error calculating current streak', { error: 'DB error' });
    });
  });
});