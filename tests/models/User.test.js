const User = require('../../src/models/User');
const pool = require('../../src/db/connection');
const logger = require('../../src/config/logger');

// Mock the database connection and logger
jest.mock('../../src/db/connection');
jest.mock('../../src/config/logger');

describe('User Model', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('findByPhone', () => {
    it('should find a user by phone number', async () => {
      const mockUser = {
        id: 1,
        phone: '+1234567890',
        status: 'LOCKED'
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.findByPhone('+1234567890');

      expect(pool.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE phone = $1',
        ['+1234567890']
      );
      expect(result).toEqual(mockUser);
    });

    it('should return null if user not found', async () => {
      pool.query.mockResolvedValue({ rows: [] });

      const result = await User.findByPhone('+9999999999');

      expect(result).toBeNull();
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.findByPhone('+1234567890')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error finding user by phone', { error: 'DB error' });
    });
  });

  describe('create', () => {
    it('should create a new user', async () => {
      const mockUser = {
        id: 1,
        phone: '+1234567890',
        status: 'LOCKED'
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.create('+1234567890');

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO users'),
        ['+1234567890', 'LOCKED']
      );
      expect(result).toEqual(mockUser);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.create('+1234567890')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error creating user', { error: 'DB error' });
    });
  });

  describe('findOrCreate', () => {
    it('should return existing user if found', async () => {
      const mockUser = {
        id: 1,
        phone: '+1234567890',
        status: 'ACTIVE'
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.findOrCreate('+1234567890');

      expect(result).toEqual(mockUser);
      expect(pool.query).toHaveBeenCalledTimes(1); // Only findByPhone called
    });

    it('should create new user if not found', async () => {
      const mockUser = {
        id: 1,
        phone: '+1234567890',
        status: 'LOCKED'
      };

      pool.query
        .mockResolvedValueOnce({ rows: [] }) // findByPhone returns nothing
        .mockResolvedValueOnce({ rows: [mockUser] }); // create returns new user

      const result = await User.findOrCreate('+1234567890');

      expect(result).toEqual(mockUser);
      expect(pool.query).toHaveBeenCalledTimes(2);
    });
  });

  describe('updateStatus', () => {
    it('should update user status successfully', async () => {
      const mockUser = { id: 1, status: 'ACTIVE' };
      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.updateStatus(1, 'ACTIVE');

      expect(pool.query).toHaveBeenCalledWith(
        'UPDATE users SET status = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
        ['ACTIVE', 1]
      );
      expect(result).toEqual(mockUser);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.updateStatus(1, 'ACTIVE')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error updating status', { error: 'DB error' });
    });
  });

  describe('updateState', () => {
    it('should update user state and context', async () => {
      const mockUser = {
        id: 1,
        current_state: 'MAIN_MENU',
        session_context: {}
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.updateState(1, 'MAIN_MENU', { test: 'data' });

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users'),
        ['MAIN_MENU', JSON.stringify({ test: 'data' }), 1]
      );
      expect(result).toEqual(mockUser);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.updateState(1, 'MAIN_MENU')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error updating state', { error: 'DB error' });
    });
  });

  describe('updateLastActive', () => {
    it('should update last active timestamp successfully', async () => {
      const mockUser = { id: 1, last_active: new Date() };
      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.updateLastActive(1);

      expect(pool.query).toHaveBeenCalledWith(
        'UPDATE users SET last_active = NOW() WHERE id = $1 RETURNING *',
        [1]
      );
      expect(result).toEqual(mockUser);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.updateLastActive(1)).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error updating last active', { error: 'DB error' });
    });
  });

  describe('setName', () => {
    it('should set display name successfully', async () => {
      const mockUser = { id: 1, display_name: 'John Doe' };
      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.setName(1, 'John Doe');

      expect(pool.query).toHaveBeenCalledWith(
        'UPDATE users SET display_name = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
        ['John Doe', 1]
      );
      expect(result).toEqual(mockUser);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.setName(1, 'John Doe')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error setting name', { error: 'DB error' });
    });
  });

  describe('setTimezone', () => {
    it('should set timezone successfully', async () => {
      const mockUser = { id: 1, timezone: 'America/New_York' };
      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.setTimezone(1, 'America/New_York');

      expect(pool.query).toHaveBeenCalledWith(
        'UPDATE users SET timezone = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
        ['America/New_York', 1]
      );
      expect(result).toEqual(mockUser);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.setTimezone(1, 'America/New_York')).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error setting timezone', { error: 'DB error' });
    });
  });

  describe('optOut', () => {
    it('should mark user as opted out', async () => {
      const mockUser = {
        id: 1,
        status: 'PAUSED',
        opted_out_at: new Date()
      };

      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.optOut(1);

      expect(pool.query).toHaveBeenCalledWith(
        expect.stringContaining('opted_out_at = NOW()'),
        [1]
      );
      expect(result.status).toBe('PAUSED');
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.optOut(1)).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error opting out user', { error: 'DB error' });
    });
  });

  describe('getInactiveSessions', () => {
    it('should return inactive sessions successfully', async () => {
      const mockSessions = [{ session_id: 'sess1', user_id: 1 }];
      pool.query.mockResolvedValue({ rows: mockSessions });

      const result = await User.getInactiveSessions(60000); // 1 min timeout

      expect(pool.query).toHaveBeenCalledWith(
        'SELECT * FROM sessions WHERE last_activity < NOW() - INTERVAL \'1 minute\'',
        []
      );
      expect(result).toEqual(mockSessions);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.getInactiveSessions(60000)).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error getting inactive sessions', { error: 'DB error' });
    });
  });

  describe('resetToOnboarding', () => {
    it('should reset user to onboarding successfully', async () => {
      const mockUser = { id: 1, current_state: 'ONBOARDING' };
      pool.query.mockResolvedValue({ rows: [mockUser] });

      const result = await User.resetToOnboarding(1);

      expect(pool.query).toHaveBeenCalledWith(
        'UPDATE users SET current_state = \'ONBOARDING\', session_context = \'{}\', status = \'LOCKED\' WHERE id = $1 RETURNING *',
        [1]
      );
      expect(result).toEqual(mockUser);
    });

    it('should throw error on query failure', async () => {
      pool.query.mockRejectedValue(new Error('DB error'));
      jest.spyOn(logger, 'error');

      await expect(User.resetToOnboarding(1)).rejects.toThrow('DB error');
      expect(logger.error).toHaveBeenCalledWith('Error resetting user to onboarding', { error: 'DB error' });
    });
  });

  // Transactional method tests (unlockWithCode) - kept from original
  describe('unlockWithCode', () => {
    it('should unlock user with valid code', async () => {
      const mockClient = {
        query: jest.fn(),
        release: jest.fn()
      };

      pool.connect.mockResolvedValue(mockClient);
      
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [{ code: 'TEST123' }] }) // Update access_codes
        .mockResolvedValueOnce({ rows: [{ id: 1, is_unlocked: true }] }) // Update users
        .mockResolvedValueOnce(); // COMMIT

      const result = await User.unlockWithCode(1, 'TEST123');

      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(mockClient.release).toHaveBeenCalled();
    });

    it('should fail with invalid code', async () => {
      const mockClient = {
        query: jest.fn(),
        release: jest.fn()
      };

      pool.connect.mockResolvedValue(mockClient);
      
      mockClient.query
        .mockResolvedValueOnce() // BEGIN
        .mockResolvedValueOnce({ rows: [] }) // No matching code
        .mockResolvedValueOnce(); // ROLLBACK

      const result = await User.unlockWithCode(1, 'INVALID');

      expect(result.success).toBe(false);
      expect(result.message).toContain('Invalid or expired');
      expect(mockClient.release).toHaveBeenCalled();
    });
  });
});