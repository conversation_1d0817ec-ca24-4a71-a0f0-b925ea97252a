const ComplianceService = require('../../src/services/complianceService');
const pool = require('../../src/db/connection');
const logger = require('../../src/config/logger');
const DatabaseSecurity = require('../../src/utils/databaseSecurity');

jest.mock('../../src/db/connection');
jest.mock('../../src/config/logger');
jest.mock('../../src/utils/databaseSecurity');

describe('ComplianceService', () => {
  let service;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
    service = require('../../src/services/complianceService');
  });

  describe('needsComplianceOnboarding', () => {
    it('should return true if any flag missing', () => {
      const user = { age_verified: false, consent_given: true, terms_accepted: true };
      expect(service.needsComplianceOnboarding(user)).toBe(true);

      const user2 = { age_verified: true, consent_given: false, terms_accepted: true };
      expect(service.needsComplianceOnboarding(user2)).toBe(true);

      const user3 = { age_verified: true, consent_given: true, terms_accepted: false };
      expect(service.needsComplianceOnboarding(user3)).toBe(true);
    });

    it('should return false if all flags present', () => {
      const user = { age_verified: true, consent_given: true, terms_accepted: true };
      expect(service.needsComplianceOnboarding(user)).toBe(false);
    });
  });

  describe('handleAgeVerification', () => {
    beforeEach(() => {
      jest.spyOn(service, 'recordAgeVerification');
      jest.spyOn(service, 'blockMinorUser');
    });

    it('should verify adult age and record', async () => {
      const user = { id: 1 };
      const mockClient = { query: jest.fn().mockResolvedValue({ rows: [] }), release: jest.fn() };
      pool.connect.mockResolvedValue(mockClient);

      const result = await service.handleAgeVerification(user, '25');

      expect(service.recordAgeVerification).toHaveBeenCalledWith(user, 25);
      expect(service.blockMinorUser).not.toHaveBeenCalled();
      expect(result).toEqual({
        message: expect.stringContaining('By continuing, you agree to our Terms and Privacy Policy.'),
        newState: 'COMBINED_CONSENT'
      });
    });

    it('should block minor and return blocked message', async () => {
      const user = { id: 1 };
      jest.spyOn(service, 'recordAgeVerification');
      const mockClient = { query: jest.fn().mockResolvedValue({ rows: [] }), release: jest.fn() };
      pool.connect.mockResolvedValue(mockClient);

      const result = await service.handleAgeVerification(user, '16');

      expect(service.blockMinorUser).toHaveBeenCalledWith(user);
      expect(service.recordAgeVerification).not.toHaveBeenCalled();
      expect(result).toEqual({
        message: expect.stringContaining('Sorry, you must be 18 or older'),
        newState: 'BLOCKED_MINOR'
      });
    });

    it('should request valid age on invalid input', async () => {
      const user = { id: 1 };
      jest.spyOn(service, 'recordAgeVerification');
      jest.spyOn(service, 'blockMinorUser');

      const result = await service.handleAgeVerification(user, 'invalid');

      expect(service.recordAgeVerification).not.toHaveBeenCalled();
      expect(service.blockMinorUser).not.toHaveBeenCalled();
      expect(result).toEqual({
        message: expect.stringContaining('Please enter a valid age'),
        newState: 'AGE_VERIFICATION'
      });
    });

    it('should handle age out of range', async () => {
      const user = { id: 1 };
      jest.spyOn(service, 'recordAgeVerification');
      jest.spyOn(service, 'blockMinorUser');

      const result = await service.handleAgeVerification(user, '5');

      expect(service.blockMinorUser).toHaveBeenCalledWith(user);
      expect(result.newState).toBe('BLOCKED_MINOR');
    });
  });

  describe('handleCombinedConsent', () => {
    beforeEach(() => {
      jest.spyOn(service, 'recordCombinedConsent');
    });

    it('should record consent on agree', async () => {
      const user = { id: 1 };
      const mockClient = { query: jest.fn().mockResolvedValue({ rows: [] }), release: jest.fn() };
      pool.connect.mockResolvedValue(mockClient);

      const result = await service.handleCombinedConsent(user, 'AGREE');

      expect(service.recordCombinedConsent).toHaveBeenCalledWith(user);
      expect(result).toEqual({
        message: expect.stringContaining("Welcome to Lockin! Let's set up your first habit."),
        newState: 'SETTING_HABIT'
      });
    });

    it('should show terms help on terms request', async () => {
      const user = { id: 1 };
      jest.spyOn(service, 'recordCombinedConsent');

      const result = await service.handleCombinedConsent(user, 'TERMS');

      expect(service.recordCombinedConsent).not.toHaveBeenCalled();
      expect(result.message).toContain('Terms: https://lockintracker.com/terms');
      expect(result.newState).toBe('COMBINED_CONSENT');
    });

    it('should handle rejection', async () => {
      const user = { id: 1 };
      jest.spyOn(service, 'recordCombinedConsent');

      const result = await service.handleCombinedConsent(user, 'NO');

      expect(service.recordCombinedConsent).not.toHaveBeenCalled();
      expect(result.message).toContain('We need your consent to provide the service');
      expect(result.newState).toBe('COMBINED_CONSENT');
    });

    it('should request clear response on invalid', async () => {
      const user = { id: 1 };
      jest.spyOn(service, 'recordCombinedConsent');

      const result = await service.handleCombinedConsent(user, 'invalid');

      expect(service.recordCombinedConsent).not.toHaveBeenCalled();
      expect(result.message).toContain('Please reply AGREE');
      expect(result.newState).toBe('COMBINED_CONSENT');
    });
  });

  describe('handlePrivacyConsent', () => {
    beforeEach(() => {
      jest.spyOn(service, 'recordPrivacyConsent');
    });

    it('should record consent on yes', async () => {
      const user = { id: 1 };
      const mockClient = { query: jest.fn().mockResolvedValue({ rows: [] }), release: jest.fn() };
      pool.connect.mockResolvedValue(mockClient);

      const result = await service.handlePrivacyConsent(user, 'YES');

      expect(service.recordPrivacyConsent).toHaveBeenCalledWith(user, true);
      expect(result.newState).toBe('TERMS_ACCEPTANCE');
    });

    it('should handle no consent', async () => {
      const user = { id: 1 };
      jest.spyOn(service, 'recordPrivacyConsent');

      const result = await service.handlePrivacyConsent(user, 'NO');

      expect(service.recordPrivacyConsent).not.toHaveBeenCalled();
      expect(result.newState).toBe('PRIVACY_CONSENT');
    });

    it('should show privacy help', async () => {
      const user = { id: 1 };
      jest.spyOn(service, 'recordPrivacyConsent');

      const result = await service.handlePrivacyConsent(user, 'PRIVACY HELP');

      expect(service.recordPrivacyConsent).not.toHaveBeenCalled();
      expect(result.message).toContain('What data do we collect?');
      expect(result.newState).toBe('PRIVACY_CONSENT');
    });
  });

  describe('handleTermsAcceptance', () => {
    beforeEach(() => {
      jest.spyOn(service, 'recordTermsAcceptance');
    });

    it('should record acceptance and go to main menu', async () => {
      const user = { id: 1 };
      const mockClient = { query: jest.fn().mockResolvedValue({ rows: [] }), release: jest.fn() };
      pool.connect.mockResolvedValue(mockClient);

      const result = await service.handleTermsAcceptance(user, 'ACCEPT TERMS');

      expect(service.recordTermsAcceptance).toHaveBeenCalledWith(user);
      expect(result.newState).toBe('MAIN_MENU');
      expect(result.message).toContain('WELCOME TO LOCKIN!');
    });

    it('should handle rejection', async () => {
      const user = { id: 1 };
      jest.spyOn(service, 'recordTermsAcceptance');

      const result = await service.handleTermsAcceptance(user, 'NO');

      expect(service.recordTermsAcceptance).not.toHaveBeenCalled();
      expect(result.newState).toBe('TERMS_ACCEPTANCE');
    });
  });

  describe('recordAgeVerification', () => {
    it('should record age verification transactionally', async () => {
      const user = { id: 1 };
      const mockClient = {
        query: jest.fn().mockResolvedValue({ rows: [] }),
        release: jest.fn()
      };
      pool.connect.mockResolvedValue(mockClient);

      await service.recordAgeVerification(user, 25);

      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining("UPDATE users SET age_verified = TRUE"),
        [1]
      );
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining("INSERT INTO user_consents"),
        [1, 'Age verified: 25 years old']
      );
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
      expect(logger.info).toHaveBeenCalledWith('Age verification recorded', { userId: 1, age: 25 });
      expect(mockClient.release).toHaveBeenCalled();
    });

    it('should rollback on error', async () => {
      const user = { id: 1 };
      const mockClient = {
        query: jest.fn(),
        release: jest.fn()
      };
      pool.connect.mockResolvedValue(mockClient);
      mockClient.query.mockRejectedValueOnce(new Error('DB error'));

      mockClient.query.mockResolvedValueOnce({ rows: [] }); // BEGIN
      // Query fails on first update

      await service.recordAgeVerification(user, 25);

      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
      expect(logger.error).toHaveBeenCalledWith('Age verification failed', { userId: 1, error: 'DB error' });
    });
  });

  // Similar tests for recordPrivacyConsent, recordCombinedConsent, recordTermsAcceptance, blockMinorUser

  describe('getComplianceStatus', () => {
    it('should return compliance flags', () => {
      const user = { age_verified: true, consent_given: false, terms_accepted: true };
      const status = service.getComplianceStatus(user);

      expect(status).toEqual({
        ageVerified: true,
        consentGiven: false,
        termsAccepted: true,
        isCompliant: false,
        needsOnboarding: true
      });
    });
  });

  describe('handleComplianceFlow', () => {
    beforeEach(() => {
      jest.spyOn(service, 'handleAgeVerification');
      jest.spyOn(service, 'handleCombinedConsent');
      jest.spyOn(service, 'handleTermsAcceptance');
      jest.spyOn(service, 'startComplianceOnboarding');
    });

    it('should route to age verification state', async () => {
      const user = { current_state: 'AGE_VERIFICATION', id: 1 };
      service.handleAgeVerification.mockResolvedValue({ message: 'test', newState: 'next' });

      const result = await service.handleComplianceFlow(user, '25');

      expect(service.handleAgeVerification).toHaveBeenCalledWith(user, '25');
      expect(result).toEqual({ message: 'test', newState: 'next' });
    });

    it('should route to combined consent', async () => {
      const user = { current_state: 'COMBINED_CONSENT' };
      service.handleCombinedConsent.mockResolvedValue({ message: 'test', newState: 'next' });

      const result = await service.handleComplianceFlow(user, 'AGREE');

      expect(service.handleCombinedConsent).toHaveBeenCalledWith(user, 'AGREE');
      expect(result).toEqual({ message: 'test', newState: 'next' });
    });

    it('should start onboarding if needed', async () => {
      const user = { 
        current_state: 'MAIN_MENU',
        age_verified: false,
        consent_given: true,
        terms_accepted: true,
        id: 1 
      };
      DatabaseSecurity.query.mockResolvedValue({ rows: [] });
      service.startComplianceOnboarding.mockResolvedValue({ message: 'start', newState: 'AGE_VERIFICATION' });

      const result = await service.handleComplianceFlow(user, 'message');

      expect(service.needsComplianceOnboarding).toHaveBeenCalledWith(user);
      expect(DatabaseSecurity.query).toHaveBeenCalledWith(
        'UPDATE users SET current_state = $1 WHERE id = $2',
        ['AGE_VERIFICATION', 1]
      );
      expect(service.startComplianceOnboarding).toHaveBeenCalledWith(user);
      expect(result).toEqual({ message: 'start', newState: 'AGE_VERIFICATION' });
    });

    it('should return null if not compliance flow', async () => {
      const user = { current_state: 'MAIN_MENU', age_verified: true, consent_given: true, terms_accepted: true };

      const result = await service.handleComplianceFlow(user, 'message');

      expect(result).toBeNull();
    });
  });

  describe('startComplianceOnboarding', () => {
    it('should update state and return onboarding message', async () => {
      const user = { id: 1 };
      DatabaseSecurity.query.mockResolvedValue({ rows: [] });

      const result = await service.startComplianceOnboarding(user);

      expect(DatabaseSecurity.query).toHaveBeenCalledWith(
        'UPDATE users SET current_state = $1 WHERE id = $2',
        ['AGE_VERIFICATION', 1]
      );
      expect(result).toEqual({
        message: expect.stringContaining("Welcome! Please confirm you're 18+"),
        newState: 'AGE_VERIFICATION'
      });
    });
  });

  describe('blockMinorUser', () => {
    it('should block minor transactionally', async () => {
      const user = { id: 1 };
      const mockClient = {
        query: jest.fn().mockResolvedValue({ rows: [] }),
        release: jest.fn()
      };
      pool.connect.mockResolvedValue(mockClient);

      await service.blockMinorUser(user);

      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining("UPDATE users SET status = 'BLOCKED'"),
        [1]
      );
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');
      expect(logger.info).toHaveBeenCalledWith('Minor user blocked', { userId: 1 });
    });

    it('should rollback on error', async () => {
      const user = { id: 1 };
      const mockClient = {
        query: jest.fn(),
        release: jest.fn()
      };
      pool.connect.mockResolvedValue(mockClient);
      mockClient.query.mockResolvedValueOnce({ rows: [] }); // BEGIN
      mockClient.query.mockRejectedValueOnce(new Error('DB error')); // Update fails

      await service.blockMinorUser(user);

      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
      expect(logger.error).toHaveBeenCalled();
    });
  });
});