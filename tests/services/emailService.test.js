const EmailService = require('../../src/services/emailService');
const emailQueue = require('../../src/services/email/emailQueue');
const emailTemplates = require('../../src/services/email/emailTemplates');

jest.mock('../../src/services/email/emailQueue');
jest.mock('../../src/services/email/emailTemplates');
jest.useFakeTimers();

describe('EmailService', () => {
  let service;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    // Don't reset modules as it breaks singleton pattern
    service = require('../../src/services/emailService');
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('processQueue', () => {
    it('should delegate to emailQueue.processQueue', async () => {
      emailQueue.processQueue.mockResolvedValue('processed');

      const result = await service.processQueue();

      expect(emailQueue.processQueue).toHaveBeenCalledTimes(1);
      expect(result).toBe('processed');
    });
  });

  describe('startQueueProcessor', () => {
    it('should start interval and process immediately', async () => {
      emailQueue.processQueue.mockResolvedValue('processed');

      service.startQueueProcessor();

      // Verify immediate processing
      expect(emailQueue.processQueue).toHaveBeenCalledTimes(1);

      // Advance time by 60 seconds (1 minute)
      jest.advanceTimersByTime(60000);

      // Should process again
      expect(emailQueue.processQueue).toHaveBeenCalledTimes(2);

      // Advance another 60 seconds
      jest.advanceTimersByTime(60000);

      expect(emailQueue.processQueue).toHaveBeenCalledTimes(3);
    });

    it('should handle queue processing errors gracefully', async () => {
      emailQueue.processQueue.mockRejectedValueOnce(new Error('Queue error')).mockResolvedValue('processed');

      service.startQueueProcessor();

      // Wait for initial promise to settle
      await new Promise(resolve => setImmediate(resolve));

      // Immediate call should not throw
      expect(emailQueue.processQueue).toHaveBeenCalledTimes(1);

      // Interval should continue despite error
      jest.advanceTimersByTime(60000);
      expect(emailQueue.processQueue).toHaveBeenCalledTimes(2);
    });
  });

  describe('template methods', () => {
    it('should delegate to emailTemplates for welcome_monthly', async () => {
      const mockData = { name: 'John', code: 'TEST123' };
      const mockTemplate = { subject: 'Welcome!', html: '<html>' };
      emailTemplates.getWelcomeMonthlyTemplate.mockReturnValue(mockTemplate);

      const result = service.getWelcomeMonthlyTemplate(mockData);

      expect(emailTemplates.getWelcomeMonthlyTemplate).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(mockTemplate);
    });

    it('should delegate to emailTemplates for welcome_yearly', async () => {
      const mockData = { name: 'John', code: 'TEST123' };
      const mockTemplate = { subject: 'Welcome Yearly!', html: '<html>' };
      emailTemplates.getWelcomeYearlyTemplate.mockReturnValue(mockTemplate);

      const result = service.getWelcomeYearlyTemplate(mockData);

      expect(emailTemplates.getWelcomeYearlyTemplate).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(mockTemplate);
    });

    it('should delegate to emailTemplates for welcome_dynamic', async () => {
      const mockData = { name: 'John', code: 'TEST123' };
      const mockTemplate = { subject: 'Welcome Dynamic!', html: '<html>' };
      emailTemplates.getWelcomeDynamicTemplate.mockReturnValue(mockTemplate);

      const result = service.getWelcomeDynamicTemplate(mockData);

      expect(emailTemplates.getWelcomeDynamicTemplate).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(mockTemplate);
    });

    it('should delegate to emailTemplates for welcome_multi_code', async () => {
      const mockData = { name: 'John', codes: ['CODE1', 'CODE2'] };
      const mockTemplate = { subject: 'Welcome Multi!', html: '<html>' };
      emailTemplates.getWelcomeMultiCodeTemplate.mockReturnValue(mockTemplate);

      const result = service.getWelcomeMultiCodeTemplate(mockData);

      expect(emailTemplates.getWelcomeMultiCodeTemplate).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(mockTemplate);
    });

    it('should delegate to emailTemplates for payment_failed', async () => {
      const mockData = { name: 'John', reason: 'Card declined' };
      const mockTemplate = { subject: 'Payment Failed', html: '<html>' };
      emailTemplates.getPaymentFailedTemplate.mockReturnValue(mockTemplate);

      const result = service.getPaymentFailedTemplate(mockData);

      expect(emailTemplates.getPaymentFailedTemplate).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(mockTemplate);
    });

    it('should delegate to emailTemplates for subscription_cancelled', async () => {
      const mockData = { name: 'John' };
      const mockTemplate = { subject: 'Subscription Cancelled', html: '<html>' };
      emailTemplates.getSubscriptionCancelledTemplate.mockReturnValue(mockTemplate);

      const result = service.getSubscriptionCancelledTemplate(mockData);

      expect(emailTemplates.getSubscriptionCancelledTemplate).toHaveBeenCalledWith(mockData);
      expect(result).toEqual(mockTemplate);
    });
  });

  describe('templates property', () => {
    it('should have bound template methods', () => {
      expect(typeof service.templates.welcome_monthly).toBe('function');
      expect(typeof service.templates.welcome_yearly).toBe('function');
      expect(typeof service.templates.welcome_dynamic).toBe('function');
      expect(typeof service.templates.welcome_multi_code).toBe('function');
      expect(typeof service.templates.payment_failed).toBe('function');
      expect(typeof service.templates.subscription_cancelled).toBe('function');

      // Verify binding works
      const mockData = { name: 'Test' };
      service.templates.welcome_monthly(mockData);
      expect(emailTemplates.getWelcomeMonthlyTemplate).toHaveBeenCalledWith(mockData);
    });
  });

  describe('singleton', () => {
    it('should be singleton instance', () => {
      const service1 = require('../../src/services/emailService');
      const service2 = require('../../src/services/emailService');

      expect(service1).toBe(service2);
    });
  });
});