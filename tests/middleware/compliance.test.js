const {
  checkStopKeywords,
  checkStartKeyword,
  checkMessageWindow
} = require('../../src/middleware/compliance');
const User = require('../../src/models/User');
const AuditLog = require('../../src/models/AuditLog');
const logger = require('../../src/config/logger');

jest.mock('../../src/models/User');
jest.mock('../../src/models/AuditLog');
jest.mock('../../src/config/logger');

describe('Compliance Middleware', () => {
  let mockReq, mockRes, mockNext;

  beforeEach(() => {
    jest.clearAllMocks();
    mockReq = {
      body: { Body: '', From: '+1234567890' }
    };
    mockRes = {
      type: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    mockNext = jest.fn();
  });

  describe('checkStopKeywords', () => {
    it('should opt out user on STOP keyword', async () => {
      mockReq.body.Body = 'STOP';
      User.findByPhone.mockResolvedValue({ id: 1, status: 'ACTIVE' });
      User.optOut.mockResolvedValue({ status: 'PAUSED' });
      AuditLog.log.mockResolvedValue();

      await checkStopKeywords(mockReq, mockRes, mockNext);

      expect(User.findByPhone).toHaveBeenCalledWith('+1234567890');
      expect(User.optOut).toHaveBeenCalledWith(1);
      expect(AuditLog.log).toHaveBeenCalledWith(1, expect.any(String), { keyword: 'STOP' });
      expect(mockRes.type).toHaveBeenCalledWith('text/xml');
      expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('You have been unsubscribed'));
      expect(mockNext).not.toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('User opted out', { userId: 1 });
    });

    it('should handle STOP WITH space', async () => {
      mockReq.body.Body = 'STOP test';
      User.findByPhone.mockResolvedValue({ id: 1, status: 'ACTIVE' });
      User.optOut.mockResolvedValue({ status: 'PAUSED' });
      AuditLog.log.mockResolvedValue();

      await checkStopKeywords(mockReq, mockRes, mockNext);

      expect(AuditLog.log).toHaveBeenCalledWith(1, expect.any(String), { keyword: 'STOP TEST' });
      expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('You have been unsubscribed'));
    });

    it('should not opt out already paused user', async () => {
      mockReq.body.Body = 'STOP';
      User.findByPhone.mockResolvedValue({ id: 1, status: 'PAUSED' });

      await checkStopKeywords(mockReq, mockRes, mockNext);

      expect(User.optOut).not.toHaveBeenCalled();
      expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('You have been unsubscribed'));
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should continue on user not found', async () => {
      mockReq.body.Body = 'STOP';
      User.findByPhone.mockResolvedValue(null);

      await checkStopKeywords(mockReq, mockRes, mockNext);

      expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('You have been unsubscribed'));
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should continue on opt-out error', async () => {
      mockReq.body.Body = 'STOP';
      User.findByPhone.mockResolvedValue({ id: 1, status: 'ACTIVE' });
      User.optOut.mockRejectedValue(new Error('Opt error'));
      logger.error.mockImplementation();

      await checkStopKeywords(mockReq, mockRes, mockNext);

      expect(logger.error).toHaveBeenCalledWith('Error processing opt-out', { error: 'Opt error' });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should call next on non-stop message', async () => {
      mockReq.body.Body = 'HELLO';

      await checkStopKeywords(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(User.findByPhone).not.toHaveBeenCalled();
    });
  });

  describe('checkStartKeyword', () => {
    it('should reactivate paused user on START', async () => {
      mockReq.body.Body = 'START';
      User.findByPhone.mockResolvedValue({ id: 1, status: 'PAUSED' });
      User.updateStatus.mockResolvedValue({ status: 'ACTIVE' });

      await checkStartKeyword(mockReq, mockRes, mockNext);

      expect(User.findByPhone).toHaveBeenCalledWith('+1234567890');
      expect(User.updateStatus).toHaveBeenCalledWith(1, 'ACTIVE');
      expect(mockRes.type).toHaveBeenCalledWith('text/xml');
      expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('Welcome back! You have been resubscribed'));
      expect(mockNext).not.toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith('User reactivated', { userId: 1 });
    });

    it('should handle SUBSCRIBE synonym', async () => {
      mockReq.body.Body = 'SUBSCRIBE';
      User.findByPhone.mockResolvedValue({ id: 1, status: 'PAUSED' });
      User.updateStatus.mockResolvedValue({ status: 'ACTIVE' });

      await checkStartKeyword(mockReq, mockRes, mockNext);

      expect(User.updateStatus).toHaveBeenCalledWith(1, 'ACTIVE');
      expect(mockRes.send).toHaveBeenCalledWith(expect.stringContaining('Welcome back!'));
    });

    it('should continue for non-paused user', async () => {
      mockReq.body.Body = 'START';
      User.findByPhone.mockResolvedValue({ id: 1, status: 'ACTIVE' });

      await checkStartKeyword(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should continue on user not found', async () => {
      mockReq.body.Body = 'START';
      User.findByPhone.mockResolvedValue(null);

      await checkStartKeyword(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should continue on update error', async () => {
      mockReq.body.Body = 'START';
      User.findByPhone.mockResolvedValue({ id: 1, status: 'PAUSED' });
      User.updateStatus.mockRejectedValue(new Error('Update error'));
      logger.error.mockImplementation();

      await checkStartKeyword(mockReq, mockRes, mockNext);

      expect(logger.error).toHaveBeenCalledWith('Error processing reactivation', { error: 'Update error' });
      expect(mockNext).toHaveBeenCalled();
    });

    it('should call next on non-start message', async () => {
      mockReq.body.Body = 'HELLO';

      await checkStartKeyword(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(User.findByPhone).not.toHaveBeenCalled();
    });
  });

  describe('checkMessageWindow', () => {
    it('should always call next (no-op implementation)', async () => {
      await checkMessageWindow(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });
  });
});