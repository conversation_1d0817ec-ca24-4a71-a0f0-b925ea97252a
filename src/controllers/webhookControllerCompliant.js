const User = require('../models/User');
const stateMachine = require('../services/unifiedStateMachine');
const sessionManager = require('../services/sessionManager');
const complianceService = require('../services/complianceService');
const userRightsService = require('../services/userRightsService');
const complianceAuditService = require('../services/complianceAuditService');
const logger = require('../config/logger');
const twilio = require('twilio');
const { hashPhone, createSafeLogObject } = require('../utils/piiHasher');

class CompliantWebhookController {
  async handleIncomingMessage(req, res) {
    const { Body: message, From: phone } = req.body;
    
    // Log incoming message with compliance audit using proper PII hashing
    await complianceAuditService.logDataAccess(null, 'message_received', {
      phoneHash: hashPhone(phone),
      messageLength: message.length,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    try {
      // Find or create user
      const user = await User.findOrCreate(phone);
      
      // Check for immediate privacy/rights commands first
      const lowerMessage = message.toLowerCase().trim();
      
      // Handle data rights commands (takes priority)
      if (lowerMessage === 'delete my data' || lowerMessage === 'delete account') {
        const response = await userRightsService.handleDeleteRequest(user);
        return this.sendResponse(res, response);
      }
      
      if (lowerMessage === 'export my data' || lowerMessage === 'download my data') {
        const response = await userRightsService.handleExportRequest(user);
        return this.sendResponse(res, response);
      }
      
      if (lowerMessage === 'stop' || lowerMessage === 'unsubscribe' || lowerMessage === 'opt out') {
        const response = await userRightsService.handleOptOutRequest(user);
        return this.sendResponse(res, response);
      }
      
      // Handle privacy help requests
      if (lowerMessage.includes('privacy help') || lowerMessage === 'privacy') {
        const response = this.getPrivacyHelpResponse();
        return this.sendResponse(res, response);
      }
      
      if (lowerMessage.includes('terms help') || lowerMessage === 'terms') {
        const response = this.getTermsHelpResponse();
        return this.sendResponse(res, response);
      }
      
      // Check if user is blocked (minor)
      if (user.current_state === 'BLOCKED_MINOR') {
        const response = {
          message: "Your account is blocked because you are under 18. Our service is only available to users 18 and older.",
          newState: 'BLOCKED_MINOR'
        };
        return this.sendResponse(res, response);
      }
      
      // Check if user needs compliance onboarding
      if (complianceService.needsComplianceOnboarding(user)) {
        const response = await complianceService.handleComplianceFlow(user, message);
        if (response) {
          return this.sendResponse(res, response);
        }
      }
      
      // Check if user opted out
      if (user.opt_out_communications && lowerMessage !== 'opt in') {
        const response = {
          message: "You have opted out of communications. Reply 'OPT IN' to resume receiving messages.",
          newState: user.current_state
        };
        return this.sendResponse(res, response);
      }
      
      // Handle opt-in request
      if (lowerMessage === 'opt in' && user.opt_out_communications) {
        await this.handleOptIn(user);
        const response = {
          message: `✅ *OPTED BACK IN*

Welcome back! You'll now receive:
• Habit tracking messages
• Service notifications
• Motivational content

Reply "MENU" to continue or "STOP" to opt out again.`,
          newState: 'MAIN_MENU'
        };
        return this.sendResponse(res, response);
      }
      
      // Validate session timeout
      const sessionValid = await sessionManager.validateSession(user);
      
      // Process message through compliance flow first
      const complianceResponse = await complianceService.handleComplianceFlow(user, message);
      if (complianceResponse) {
        return this.sendResponse(res, complianceResponse);
      }
      
      // Process message through state machine
      let response;
      if (!sessionValid) {
        response = {
          message: `Your session has timed out. Let's start fresh!

*Your Data Rights:*
• "EXPORT MY DATA" - Download your data
• "DELETE MY DATA" - Delete account
• "STOP" - Opt out of messages

Type 'menu' to see your options.`,
          newState: user.current_state
        };
      } else {
        response = await stateMachine.processMessage(user, message);
      }
      
      // Add legal footer to main interactions
      if (response && this.shouldAddLegalFooter(response.newState)) {
        response.message = this.addLegalFooter(response.message);
      }
      
      // Send response
      return this.sendResponse(res, response);
      
    } catch (error) {
      logger.error('Error handling webhook', { 
        error: error.message,
        phone: phone.substring(0, 5) + '***', // Redacted for privacy
        stack: error.stack
      });
      
      // Send error response
      const response = {
        message: `Sorry, something went wrong. Please try again later.

*Your Rights:*
• "EXPORT MY DATA" - Download your data
• "DELETE MY DATA" - Delete account
• "HELP" - Get support

🔒 Privacy: https://lockintracker.com/privacy`,
        newState: null
      };
      
      return this.sendResponse(res, response);
    }
  }

  /**
   * Send response with proper TwiML formatting
   */
  sendResponse(res, response) {
    const twiml = new twilio.twiml.MessagingResponse();
    
    /*******************************************************************
     * LOCKED: TWO-MESSAGE COMPLETION FLOW - DO NOT MODIFY
     * This exact two-message sequence is critical for the habit logging
     * completion experience. The first message contains shareable content,
     * the second contains navigation instructions.
     * Any changes will break the user experience.
     *******************************************************************/
    // Check if we need to send a follow-up message (for completion screen)
    if (response.sendFollowUp) {
      // MESSAGE 1: Send shareable content first
      const messages = this.splitMessage(response.message);
      messages.forEach(msg => {
        twiml.message(msg);
      });
      
      // MESSAGE 2: Send instructions second
      const followUpMessage = `Click and hold the previous message to forward or share.

1️⃣ Edit today's habits
2️⃣ Back to menu

Make a selection (reply with a number).`;
      
      twiml.message(followUpMessage);
    } else {
      // Normal single message
      const messages = this.splitMessage(response.message);
      messages.forEach(msg => {
        twiml.message(msg);
      });
    }
    
    res.type('text/xml');
    res.send(twiml.toString());
  }

  /**
   * Handle user opting back in
   */
  async handleOptIn(user) {
    const pool = require('../db/connection');
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      await client.query(
        'UPDATE users SET opt_out_communications = FALSE, opt_out_date = NULL WHERE id = $1',
        [user.id]
      );
      
      await client.query(
        `INSERT INTO user_consents (user_id, consent_type, consent_given, consent_method, notes)
         VALUES ($1, 'communications', TRUE, 'whatsapp', 'User opted back in to communications')`,
        [user.id]
      );
      
      await client.query('COMMIT');
      logger.info('User opted back in', { userId: user.id });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Opt-in failed', { userId: user.id, error: error.message });
    } finally {
      client.release();
    }
  }

  /**
   * Get privacy help response
   */
  getPrivacyHelpResponse() {
    return {
      message: `🔒 *PRIVACY HELP*

*Your Data Rights:*
• "EXPORT MY DATA" - Download all your data
• "DELETE MY DATA" - Permanently delete account
• "STOP" - Opt out of all messages

*What we collect:*
• Phone number (WhatsApp requirement)
• Habits you create
• Daily completion logs
• Payment info (if you subscribe)

*What we don't do:*
❌ Sell your data
❌ Share with marketers
❌ Excessive tracking

*Security:*
✅ Encrypted transmission
✅ Secure servers
✅ Regular audits

🔒 Full Privacy Policy: https://lockintracker.com/privacy

Questions? Reply "HELP" for support.`,
      newState: null
    };
  }

  /**
   * Get terms help response
   */
  getTermsHelpResponse() {
    return {
      message: `📋 *TERMS HELP*

*Key Points:*
• Service costs $5/month or $30/year
• You can cancel anytime
• 7-day refund policy
• Must be 18+ to use
• Fair use policies

*Your Account:*
• You control your habits and data
• We provide the tracking service
• Cancel subscription anytime
• Account deletion available

*Liability:*
• Standard limitation of liability
• Service provided "as is"
• Dispute resolution via arbitration

📋 Full Terms: https://lockintracker.com/terms

Questions? Reply "HELP" for support.`,
      newState: null
    };
  }

  /**
   * Check if legal footer should be added
   */
  shouldAddLegalFooter(state) {
    const footerStates = ['MAIN_MENU', 'PAYMENT_REQUIRED', 'SUBSCRIPTION_EXPIRED'];
    return footerStates.includes(state);
  }

  /**
   * Add legal footer to message
   */
  addLegalFooter(message) {
    if (message && !message.includes('Privacy:') && !message.includes('privacy')) {
      return message + `

🔒 Privacy: https://lockintracker.com/privacy
📋 Terms: https://lockintracker.com/terms
❓ "HELP" for support`;
    }
    return message;
  }

  /**
   * Split messages that exceed WhatsApp's character limit
   */
  splitMessage(text, maxLength = 4000) {
    if (!text || text.length <= maxLength) {
      return [text || "Sorry, no response available."];
    }
    
    const messages = [];
    let currentMessage = '';
    const lines = text.split('\n');
    
    for (const line of lines) {
      if (currentMessage.length + line.length + 1 <= maxLength) {
        currentMessage += (currentMessage ? '\n' : '') + line;
      } else {
        if (currentMessage) {
          messages.push(currentMessage);
        }
        currentMessage = line;
      }
    }
    
    if (currentMessage) {
      messages.push(currentMessage);
    }
    
    return messages.length > 0 ? messages : ["Message too long to display."];
  }

  /**
   * Verify Twilio webhook signature
   */
  verifyWebhook(req, res, next) {
    const webhookUrl = process.env.TWILIO_WEBHOOK_URL || req.protocol + '://' + req.get('host') + req.originalUrl;
    const signature = req.headers['x-twilio-signature'];
    
    if (!signature) {
      logger.warn('Missing Twilio signature');
      return res.status(400).send('Missing signature');
    }
    
    const isValid = twilio.validateRequest(
      process.env.TWILIO_AUTH_TOKEN,
      signature,
      webhookUrl,
      req.body
    );
    
    if (!isValid) {
      logger.warn('Invalid Twilio signature');
      return res.status(403).send('Invalid signature');
    }
    
    next();
  }
}

module.exports = new CompliantWebhookController();