const rateLimit = require('express-rate-limit');
const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const { RATE_LIMIT, AUDIT_EVENTS } = require('../config/constants');
const logger = require('../config/logger');

// Store violation counts for progressive penalties
const violationCounts = new Map();

// Get violation count for a key
const getViolationCount = (key) => {
  return violationCounts.get(key) || 0;
};

// Apply progressive penalty
const applyPenalty = async (key, penaltyMinutes) => {
  const currentCount = getViolationCount(key);
  violationCounts.set(key, currentCount + 1);
  
  // Clear violation count after penalty period
  setTimeout(() => {
    violationCounts.delete(key);
  }, penaltyMinutes * 60 * 1000);
};

// Create rate limiter with progressive penalties
const createRateLimiter = () => {
  return rateLimit({
    windowMs: RATE_LIMIT.WINDOW_MS,
    max: RATE_LIMIT.MAX_REQUESTS,
    message: RATE_LIMIT.MESSAGE,
    standardHeaders: true,
    legacyHeaders: false,
    
    // Custom key generator based on phone number
    keyGenerator: (req) => {
      return req.body?.From || req.ip;
    },
    
    // Custom handler for rate limit exceeded with progressive penalties
    handler: async (req, res) => {
      const phone = req.body?.From;
      const key = phone || req.ip;
      
      // Calculate progressive penalty
      const violations = getViolationCount(key);
      const penaltyMinutes = Math.min(60, Math.pow(2, violations) * 5); // Exponential backoff: 5, 10, 20, 40, 60 max
      
      await applyPenalty(key, penaltyMinutes);
      
      if (phone) {
        try {
          const user = await User.findByPhone(phone);
          if (user) {
            await AuditLog.log(user.id, AUDIT_EVENTS.RATE_LIMITED, {
              ip: req.ip,
              attempts: req.rateLimit.current,
              penaltyMinutes,
              violations: violations + 1
            });
          }
        } catch (error) {
          logger.error('Error logging rate limit', { error: error.message });
        }
      }
      
      logger.warn('Rate limit exceeded with progressive penalty', { 
        phone: phone ? '[REDACTED]' : 'unknown',
        ip: req.ip,
        penaltyMinutes,
        violations: violations + 1
      });
      
      const message = violations > 0 
        ? `Rate limit exceeded. Due to repeated violations, please wait ${penaltyMinutes} minutes before trying again.`
        : RATE_LIMIT.MESSAGE;
      
      const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Message>${message}</Message>
</Response>`;
      
      res.status(429).type('text/xml').send(twiml);
    },
    
    // Count all requests (don't skip successful ones)
    skipSuccessfulRequests: RATE_LIMIT.SKIP_SUCCESSFUL_REQUESTS !== undefined 
      ? RATE_LIMIT.SKIP_SUCCESSFUL_REQUESTS 
      : false,
    
    // Don't skip failed requests either
    skipFailedRequests: false
  });
};

// Per-user rate limiting (more granular)
const perUserRateLimiter = async (req, res, next) => {
  const phone = req.body?.From;
  
  if (!phone) {
    return next();
  }
  
  try {
    const user = await User.findByPhone(phone);
    
    if (user) {
      // Check if user has been making too many requests
      const recentLogs = await AuditLog.getByUser(user.id, 10);
      const rateLimitLogs = recentLogs.filter(log => 
        log.event_type === AUDIT_EVENTS.RATE_LIMITED &&
        new Date(log.timestamp) > new Date(Date.now() - 3600000) // Last hour
      );
      
      if (rateLimitLogs.length >= 3) {
        // User has been rate limited 3+ times in the last hour - apply stricter limits
        logger.warn('Applying strict rate limit to user', { userId: user.id });
        
        const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Message>You have been temporarily restricted due to excessive requests. Please try again later.</Message>
</Response>`;
        
        return res.status(429).type('text/xml').send(twiml);
      }
    }
  } catch (error) {
    logger.error('Error in per-user rate limiter', { error: error.message });
  }
  
  next();
};

module.exports = {
  globalRateLimiter: createRateLimiter(),
  perUserRateLimiter
};