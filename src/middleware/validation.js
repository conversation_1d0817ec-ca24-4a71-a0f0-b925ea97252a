// Enhanced validation module with comprehensive security
// Re-exports from the enhanced validation module for backward compatibility
const enhancedValidation = require('./enhancedValidation');

module.exports = {
  validateWebhook: enhancedValidation.validateWebhook,
  validatePhone: enhancedValidation.validatePhone,
  sanitizeInput: enhancedValidation.sanitizeInput,
  // Additional exports from enhanced module
  validatePaymentData: enhancedValidation.validatePaymentData,
  sanitizeEmail: enhancedValidation.sanitizeEmail,
  sanitizeURL: enhancedValidation.sanitizeURL,
  sanitizeNumber: enhancedValidation.sanitizeNumber,
  safeSQLIdentifier: enhancedValidation.safeSQLIdentifier
};