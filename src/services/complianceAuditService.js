const pool = require('../db/connection');
const logger = require('../config/logger');
const sqlIntervalBuilder = require('../utils/sqlIntervalBuilder');
const crypto = require('crypto');
const DatabaseSecurity = require('../utils/databaseSecurity');

class ComplianceAuditService {
  
  /**
   * Log data access events for compliance
   */
  async logDataAccess(userId, action, details = {}) {
    try {
      // Redact sensitive information but maintain audit trail
      const sanitizedDetails = this.sanitizeAuditData(details);
      
      await DatabaseSecurity.query(`
        INSERT INTO audit_log (
          user_id, 
          action, 
          resource_type, 
          notes, 
          timestamp,
          ip_address,
          session_id
        ) VALUES ($1, $2, 'data_access', $3, NOW(), $4, $5)
      `, [
        userId, 
        action, 
        JSON.stringify(sanitizedDetails),
        details.ipAddress || null,
        details.sessionId || null
      ]);
      
      logger.info('Data access logged', { 
        userId: userId ? userId.toString().substring(0, 3) + '***' : null, 
        action, 
        timestamp: new Date().toISOString() 
      });
    } catch (error) {
      logger.error('Audit logging failed', { userId, action, error: error.message });
    }
  }

  /**
   * Log data modification events
   */
  async logDataModification(userId, action, resourceType, resourceId, oldValues, newValues, details = {}) {
    try {
      const sanitizedOld = this.sanitizeAuditData(oldValues);
      const sanitizedNew = this.sanitizeAuditData(newValues);
      
      await DatabaseSecurity.query(`
        INSERT INTO audit_log (
          user_id, 
          action, 
          resource_type, 
          resource_id,
          old_values,
          new_values,
          notes, 
          timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
      `, [
        userId, 
        action, 
        resourceType,
        resourceId,
        JSON.stringify(sanitizedOld),
        JSON.stringify(sanitizedNew),
        JSON.stringify(this.sanitizeAuditData(details))
      ]);
      
      logger.info('Data modification logged', { 
        userId: userId ? userId.toString().substring(0, 3) + '***' : null, 
        action, 
        resourceType,
        resourceId 
      });
    } catch (error) {
      logger.error('Data modification audit failed', { userId, action, error: error.message });
    }
  }

  /**
   * Log compliance events (consent, data rights requests, etc.)
   */
  async logComplianceEvent(userId, event, details = {}) {
    try {
      const sanitizedDetails = this.sanitizeAuditData(details);
      
      await DatabaseSecurity.query(`
        INSERT INTO audit_log (
          user_id, 
          action, 
          resource_type, 
          notes, 
          timestamp
        ) VALUES ($1, $2, 'compliance_event', $3, NOW())
      `, [
        userId, 
        event, 
        JSON.stringify({
          ...sanitizedDetails,
          legalBasis: this.determineLegalBasis(event),
          retentionPeriod: this.getRetentionPeriod(event)
        })
      ]);
      
      logger.info('Compliance event logged', { 
        userId: userId ? userId.toString().substring(0, 3) + '***' : null, 
        event 
      });
    } catch (error) {
      logger.error('Compliance event audit failed', { userId, event, error: error.message });
    }
  }

  /**
   * Log security events
   */
  async logSecurityEvent(userId, event, severity, details = {}) {
    try {
      const sanitizedDetails = this.sanitizeAuditData(details);
      
      await DatabaseSecurity.query(`
        INSERT INTO audit_log (
          user_id, 
          action, 
          resource_type, 
          notes, 
          timestamp
        ) VALUES ($1, $2, 'security_event', $3, NOW())
      `, [
        userId, 
        `${severity}_${event}`, 
        JSON.stringify({
          ...sanitizedDetails,
          severity,
          requiresAlert: severity === 'critical' || severity === 'high'
        })
      ]);
      
      logger.warn('Security event logged', { 
        userId: userId ? userId.toString().substring(0, 3) + '***' : null, 
        event, 
        severity 
      });
      
      // Alert on critical security events
      if (severity === 'critical') {
        await this.alertSecurityTeam(event, sanitizedDetails);
      }
    } catch (error) {
      logger.error('Security event audit failed', { userId, event, error: error.message });
    }
  }

  /**
   * Log data retention events
   */
  async logRetentionEvent(action, details = {}) {
    try {
      await DatabaseSecurity.query(`
        INSERT INTO audit_log (
          user_id, 
          action, 
          resource_type, 
          notes, 
          timestamp
        ) VALUES (NULL, $1, 'data_retention', $2, NOW())
      `, [
        action, 
        JSON.stringify({
          ...details,
          complianceFramework: 'GDPR Article 5(1)(e) - Storage limitation',
          automatedProcess: true
        })
      ]);
      
      logger.info('Data retention event logged', { action, details });
    } catch (error) {
      logger.error('Data retention audit failed', { action, error: error.message });
    }
  }

  /**
   * Sanitize audit data to remove PII while maintaining audit trail integrity
   */
  sanitizeAuditData(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = { ...data };
    
    // Hash phone numbers instead of storing plaintext
    if (sanitized.phone) {
      sanitized.phoneHash = this.hashPII(sanitized.phone);
      delete sanitized.phone;
    }
    
    // Hash email addresses
    if (sanitized.email) {
      sanitized.emailHash = this.hashPII(sanitized.email);
      delete sanitized.email;
    }
    
    // Truncate or hash message content
    if (sanitized.messageContent) {
      sanitized.messageLength = sanitized.messageContent.length;
      sanitized.messageHash = this.hashPII(sanitized.messageContent);
      delete sanitized.messageContent;
    }
    
    // Remove other potential PII
    const piiFields = ['address', 'name', 'displayName', 'fullName'];
    piiFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[`${field}Hash`] = this.hashPII(sanitized[field]);
        delete sanitized[field];
      }
    });
    
    return sanitized;
  }

  /**
   * Hash PII data for audit trail
   */
  hashPII(data) {
    return crypto.createHash('sha256').update(data + process.env.AUDIT_SALT || 'default_salt').digest('hex').substring(0, 12);
  }

  /**
   * Determine legal basis for data processing event
   */
  determineLegalBasis(event) {
    const legalBases = {
      'consent_given': 'GDPR Article 6(1)(a) - Consent',
      'consent_withdrawn': 'GDPR Article 6(1)(a) - Consent withdrawn',
      'data_export_requested': 'GDPR Article 15 - Right of access',
      'data_deletion_requested': 'GDPR Article 17 - Right to erasure',
      'opt_out_requested': 'GDPR Article 21 - Right to object',
      'account_created': 'GDPR Article 6(1)(b) - Contract performance',
      'payment_processed': 'GDPR Article 6(1)(b) - Contract performance',
      'service_provision': 'GDPR Article 6(1)(b) - Contract performance',
      'legal_compliance': 'GDPR Article 6(1)(c) - Legal obligation',
      'security_monitoring': 'GDPR Article 6(1)(f) - Legitimate interest'
    };
    
    return legalBases[event] || 'GDPR Article 6(1)(f) - Legitimate interest';
  }

  /**
   * Get data retention period for event type
   */
  getRetentionPeriod(event) {
    const retentionPeriods = {
      'consent_given': '7 years',
      'consent_withdrawn': '7 years',
      'data_export_requested': '7 years',
      'data_deletion_requested': '7 years',
      'payment_processed': '7 years',
      'security_event': '2 years',
      'login_attempt': '1 year',
      'service_usage': '1 year'
    };
    
    return retentionPeriods[event] || '2 years';
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(startDate, endDate) {
    try {
      const report = await pool.query(`
        SELECT 
          action,
          resource_type,
          COUNT(*) as event_count,
          MIN(timestamp) as first_occurrence,
          MAX(timestamp) as last_occurrence
        FROM audit_log 
        WHERE timestamp BETWEEN $1 AND $2
        GROUP BY action, resource_type
        ORDER BY event_count DESC
      `, [startDate, endDate]);
      
      const userActivityReport = await pool.query(`
        SELECT 
          COUNT(DISTINCT user_id) as unique_users,
          COUNT(*) as total_events,
          AVG(CASE WHEN action LIKE '%_requested' THEN 1 ELSE 0 END) as avg_requests_per_user
        FROM audit_log 
        WHERE timestamp BETWEEN $1 AND $2
        AND user_id IS NOT NULL
      `, [startDate, endDate]);
      
      const securityReport = await pool.query(`
        SELECT 
          COUNT(*) as security_events,
          COUNT(CASE WHEN action LIKE 'critical_%' THEN 1 END) as critical_events,
          COUNT(CASE WHEN action LIKE 'high_%' THEN 1 END) as high_severity_events
        FROM audit_log 
        WHERE timestamp BETWEEN $1 AND $2
        AND resource_type = 'security_event'
      `, [startDate, endDate]);
      
      return {
        reportPeriod: { startDate, endDate },
        eventSummary: report.rows,
        userActivity: userActivityReport.rows[0],
        security: securityReport.rows[0],
        generatedAt: new Date().toISOString(),
        totalEvents: report.rows.reduce((sum, row) => sum + parseInt(row.event_count), 0)
      };
    } catch (error) {
      logger.error('Compliance report generation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Check for suspicious activity patterns
   */
  async detectSuspiciousActivity() {
    try {
      // Check for unusual data export patterns
      const hours24Interval = sqlIntervalBuilder.buildMakeInterval(24, 'hours');
      const suspiciousExports = await pool.query(`
        SELECT user_id, COUNT(*) as export_count
        FROM audit_log 
        WHERE action = 'data_export_requested'
        AND timestamp > NOW() - ${hours24Interval.query}
        GROUP BY user_id
        HAVING COUNT(*) > 5
      `, [hours24Interval.value]);
      
      // Check for unusual deletion patterns
      const hour1Interval = sqlIntervalBuilder.buildMakeInterval(1, 'hour');
      const suspiciousDeletions = await pool.query(`
        SELECT user_id, COUNT(*) as deletion_attempts
        FROM audit_log 
        WHERE action = 'data_deletion_requested'
        AND timestamp > NOW() - ${hour1Interval.query}
        GROUP BY user_id
        HAVING COUNT(*) > 3
      `, [hour1Interval.value]);
      
      // Log suspicious activity
      if (suspiciousExports.rows.length > 0 || suspiciousDeletions.rows.length > 0) {
        await this.logSecurityEvent(null, 'suspicious_activity_detected', 'medium', {
          suspiciousExports: suspiciousExports.rows.length,
          suspiciousDeletions: suspiciousDeletions.rows.length
        });
      }
      
      return {
        suspiciousExports: suspiciousExports.rows,
        suspiciousDeletions: suspiciousDeletions.rows,
        detected: suspiciousExports.rows.length > 0 || suspiciousDeletions.rows.length > 0
      };
    } catch (error) {
      logger.error('Suspicious activity detection failed', { error: error.message });
      return { detected: false, error: error.message };
    }
  }

  /**
   * Alert security team (placeholder - implement based on your alerting system)
   */
  async alertSecurityTeam(event, details) {
    // This would integrate with your alerting system (email, Slack, PagerDuty, etc.)
    logger.error('SECURITY ALERT', { event, details });
    
    // Log the alert
    await DatabaseSecurity.query(`
      INSERT INTO audit_log (
        action, 
        resource_type, 
        notes, 
        timestamp
      ) VALUES ('security_alert_sent', 'system', $1, NOW())
    `, [JSON.stringify({ event, details, alertedAt: new Date().toISOString() })]);
  }

  /**
   * Get audit statistics for compliance dashboard
   */
  async getAuditStats() {
    try {
      const days30Interval = sqlIntervalBuilder.buildMakeInterval(30, 'days');
      const stats = await pool.query(`
        SELECT 
          COUNT(*) as total_events,
          COUNT(DISTINCT user_id) as unique_users,
          COUNT(CASE WHEN resource_type = 'compliance_event' THEN 1 END) as compliance_events,
          COUNT(CASE WHEN resource_type = 'security_event' THEN 1 END) as security_events,
          COUNT(CASE WHEN resource_type = 'data_access' THEN 1 END) as data_access_events,
          COUNT(CASE WHEN action LIKE '%_requested' THEN 1 END) as user_requests,
          MIN(timestamp) as oldest_event,
          MAX(timestamp) as newest_event
        FROM audit_log
        WHERE timestamp > NOW() - ${days30Interval.query}
      `, [days30Interval.value]);
      
      return {
        ...stats.rows[0],
        periodDays: 30,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Audit stats generation failed', { error: error.message });
      throw error;
    }
  }
}

module.exports = new ComplianceAuditService();