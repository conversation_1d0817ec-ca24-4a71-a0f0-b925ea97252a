// Enhanced session management with secure token support
// This module maintains backward compatibility while adding enhanced security features
// For new implementations, prefer using secureSessionManager directly

const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const { SESSION_TIMEOUT_MS, STATES, AUDIT_EVENTS } = require('../config/constants');
const logger = require('../config/logger');

// Use secure session manager if enhanced security is enabled
const useSecureSessions = process.env.SECURE_SESSIONS === 'true';
const secureSessionManager = useSecureSessions ? require('./secureSessionManager') : null;

class SessionManager {
  constructor() {
    this.checkInterval = null;
    this.secureMode = useSecureSessions;
    
    // Reduced timeout for enhanced security (15 minutes instead of 30)
    this.ENHANCED_TIMEOUT_MS = 15 * 60 * 1000;
  }

  start() {
    if (this.secureMode && secureSessionManager) {
      // Delegate to secure session manager
      secureSessionManager.start();
      logger.info('Session manager started in SECURE mode');
    } else {
      // Legacy mode - check for timed-out sessions every minute
      this.checkInterval = setInterval(() => {
        this.cleanupInactiveSessions();
      }, 60000); // 1 minute

      logger.info('Session manager started in LEGACY mode');
    }
  }

  stop() {
    if (this.secureMode && secureSessionManager) {
      secureSessionManager.stop();
    } else {
      if (this.checkInterval) {
        clearInterval(this.checkInterval);
        this.checkInterval = null;
      }
    }
    logger.info('Session manager stopped');
  }

  async cleanupInactiveSessions() {
    try {
      // Use enhanced timeout if in transition mode
      const timeout = this.secureMode ? this.ENHANCED_TIMEOUT_MS : SESSION_TIMEOUT_MS;
      const inactiveUsers = await User.getInactiveSessions(timeout);
      
      for (const user of inactiveUsers) {
        await this.resetUserSession(user);
      }

      if (inactiveUsers.length > 0) {
        logger.info(`Cleaned up ${inactiveUsers.length} inactive sessions`);
      }
    } catch (error) {
      logger.error('Error cleaning up inactive sessions', { error: error.message });
    }
  }

  async resetUserSession(user) {
    try {
      // Reset user to main menu
      await User.updateState(user.id, STATES.MAIN_MENU, {});
      
      // Log the timeout event
      await AuditLog.log(user.id, AUDIT_EVENTS.SESSION_TIMEOUT, {
        previousState: user.current_state,
        lastActive: user.last_active,
        secureMode: this.secureMode
      });

      logger.debug(`Session timeout for user ${user.id}`);
    } catch (error) {
      logger.error('Error resetting user session', { 
        userId: user.id, 
        error: error.message 
      });
    }
  }

  async validateSession(user) {
    // If using secure sessions, delegate to secure manager
    if (this.secureMode && secureSessionManager) {
      const phoneNumber = user.phone_number;
      const sessionResult = secureSessionManager.getSessionByPhone(phoneNumber);
      
      if (!sessionResult) {
        // No valid session, create new one if user is active
        if (user.current_state !== STATES.MAIN_MENU) {
          await this.resetUserSession(user);
          return false;
        }
        return true;
      }
      
      // Session is valid
      return true;
    }
    
    // Legacy validation
    const lastActive = new Date(user.last_active);
    const now = new Date();
    const timeSinceActive = now - lastActive;
    const timeout = this.secureMode ? this.ENHANCED_TIMEOUT_MS : SESSION_TIMEOUT_MS;

    if (timeSinceActive > timeout && user.current_state !== STATES.MAIN_MENU) {
      await this.resetUserSession(user);
      return false;
    }

    return true;
  }

  // New method for creating secure sessions
  createSecureSession(userId, phoneNumber) {
    if (this.secureMode && secureSessionManager) {
      return secureSessionManager.createSession(userId, phoneNumber);
    }
    // Legacy mode doesn't use tokens
    return null;
  }

  // Get session statistics
  getStats() {
    if (this.secureMode && secureSessionManager) {
      return secureSessionManager.getStats();
    }
    return {
      mode: 'legacy',
      secureSessionsEnabled: false
    };
  }
}

module.exports = new SessionManager();