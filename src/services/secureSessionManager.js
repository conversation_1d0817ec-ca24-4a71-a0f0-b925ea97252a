const crypto = require('crypto');
const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const { STATES, AUDIT_EVENTS } = require('../config/constants');
const logger = require('../config/logger');

class SecureSessionManager {
  constructor() {
    this.sessions = new Map();
    this.SESSION_TIMEOUT = 15 * 60 * 1000; // 15 minutes (reduced from 30)
    this.TOKEN_ROTATION = 5 * 60 * 1000; // Rotate every 5 minutes
    this.checkInterval = null;
    this.cleanupInterval = null;
  }

  generateSecureToken() {
    // Generate cryptographically secure random token
    return crypto.randomBytes(32).toString('hex');
  }

  createSession(userId, phoneNumber) {
    const token = this.generateSecureToken();
    const now = Date.now();
    
    const session = {
      token,
      userId,
      phoneNumber,
      createdAt: now,
      lastActivity: now,
      rotationDue: now + this.TOKEN_ROTATION,
      rotationCount: 0
    };
    
    this.sessions.set(token, session);
    
    // Also store by phone number for quick lookup
    this.sessions.set(`phone:${phoneNumber}`, token);
    
    logger.debug('Secure session created', { 
      userId, 
      tokenPrefix: token.substring(0, 8) + '...' 
    });
    
    return token;
  }

  validateAndRotate(token) {
    const session = this.sessions.get(token);
    if (!session) {
      logger.debug('Session not found', { tokenPrefix: token?.substring(0, 8) + '...' });
      return null;
    }
    
    const now = Date.now();
    
    // Check timeout
    if (now - session.lastActivity > this.SESSION_TIMEOUT) {
      this.destroySession(token);
      logger.debug('Session timeout', { userId: session.userId });
      return null;
    }
    
    // Update activity timestamp
    session.lastActivity = now;
    
    // Check if token rotation is due
    if (now > session.rotationDue) {
      const newToken = this.rotateToken(session, token);
      logger.debug('Token rotated', { 
        userId: session.userId,
        rotationCount: session.rotationCount 
      });
      return { session, newToken, rotated: true };
    }
    
    return { session, newToken: null, rotated: false };
  }

  rotateToken(session, oldToken) {
    const newToken = this.generateSecureToken();
    
    // Delete old token
    this.sessions.delete(oldToken);
    
    // Update session with new token
    session.token = newToken;
    session.rotationDue = Date.now() + this.TOKEN_ROTATION;
    session.rotationCount++;
    
    // Store with new token
    this.sessions.set(newToken, session);
    
    // Update phone lookup
    this.sessions.set(`phone:${session.phoneNumber}`, newToken);
    
    return newToken;
  }

  getSessionByPhone(phoneNumber) {
    const token = this.sessions.get(`phone:${phoneNumber}`);
    if (!token) return null;
    
    return this.validateAndRotate(token);
  }

  destroySession(token) {
    const session = this.sessions.get(token);
    if (session) {
      // Remove both token and phone lookup
      this.sessions.delete(token);
      this.sessions.delete(`phone:${session.phoneNumber}`);
      
      logger.debug('Session destroyed', { userId: session.userId });
    }
  }

  // Constant-time comparison for token validation
  secureCompare(a, b) {
    if (!a || !b || a.length !== b.length) {
      return false;
    }
    
    return crypto.timingSafeEqual(
      Buffer.from(a),
      Buffer.from(b)
    );
  }

  async cleanupInactiveSessions() {
    try {
      const now = Date.now();
      const sessionsToRemove = [];
      
      // Find expired sessions
      for (const [key, value] of this.sessions.entries()) {
        // Skip phone lookups
        if (typeof key === 'string' && key.startsWith('phone:')) {
          continue;
        }
        
        const session = value;
        if (now - session.lastActivity > this.SESSION_TIMEOUT) {
          sessionsToRemove.push(key);
          
          // Reset user to main menu
          try {
            await User.updateState(session.userId, STATES.MAIN_MENU, {});
            await AuditLog.log(session.userId, AUDIT_EVENTS.SESSION_TIMEOUT, {
              sessionDuration: now - session.createdAt,
              rotationCount: session.rotationCount
            });
          } catch (error) {
            logger.error('Error resetting user session', { 
              userId: session.userId,
              error: error.message 
            });
          }
        }
      }
      
      // Remove expired sessions
      for (const key of sessionsToRemove) {
        this.destroySession(key);
      }
      
      if (sessionsToRemove.length > 0) {
        logger.info(`Cleaned up ${sessionsToRemove.length} inactive secure sessions`);
      }
    } catch (error) {
      logger.error('Error cleaning up secure sessions', { error: error.message });
    }
  }

  start() {
    // Check for timed-out sessions every minute
    this.checkInterval = setInterval(() => {
      this.cleanupInactiveSessions();
    }, 60000); // 1 minute
    
    // Memory cleanup every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.performMemoryCleanup();
    }, 5 * 60000);
    
    logger.info('Secure session manager started');
  }

  stop() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    // Clear all sessions
    this.sessions.clear();
    
    logger.info('Secure session manager stopped');
  }

  performMemoryCleanup() {
    const sessionCount = Array.from(this.sessions.keys())
      .filter(key => typeof key !== 'string' || !key.startsWith('phone:'))
      .length;
    
    logger.debug('Session manager memory status', {
      sessionCount,
      memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB'
    });
  }

  getStats() {
    const sessions = Array.from(this.sessions.values())
      .filter(val => typeof val === 'object' && val.token);
    
    const now = Date.now();
    
    return {
      totalSessions: sessions.length,
      activeSessions: sessions.filter(s => now - s.lastActivity < 5 * 60000).length,
      pendingRotation: sessions.filter(s => now > s.rotationDue).length,
      avgRotationCount: sessions.length > 0 
        ? Math.round(sessions.reduce((sum, s) => sum + s.rotationCount, 0) / sessions.length)
        : 0
    };
  }
}

module.exports = new SecureSessionManager();