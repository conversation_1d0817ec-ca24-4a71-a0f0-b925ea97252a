/**
 * Test webhook payloads for ThriveCart integration with 4 separate products and bump orders
 * Updated structure to support:
 * - Annual ($39.99) with friend (+$20) and tribe (+$60) bumps
 * - Monthly ($5.99), Weekly ($2.99), Lifetime ($99.99) without bumps
 */

const testPayloads = {
  // 1. Annual subscription without bumps
  annual_basic: {
    event: 'order.success',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-ANN-001',
      email: '<EMAIL>',
      first_name: 'Annual',
      last_name: 'User',
      name: 'Annual User'
    },
    order: {
      order_id: 'ORD-ANN-001',
      total: 3999, // $39.99 in cents
      total_str: '39.99',
      currency: 'USD',
      charges: [
        {
          name: 'Lock In Annual Subscription',
          amount: 3999,
          payment_plan_name: 'Annual Subscription',
          payment_plan_id: 'plan_annual_001'
        }
      ],
      future_charges: [
        {
          due: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          frequency: 'year',
          frequency_days: 365,
          amount: 3999
        }
      ]
    },
    product: {
      name: 'Lock In Annual Subscription'
    },
    product_name: 'Lock In Annual Subscription'
  },

  // 2. Annual subscription with friend bump
  annual_friend_bump: {
    event: 'order.success',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-ANN-002',
      email: '<EMAIL>',
      first_name: 'Annual',
      last_name: 'Friend',
      name: 'Annual Friend'
    },
    order: {
      order_id: 'ORD-ANN-002',
      total: 5999, // $59.99 in cents ($39.99 + $20.00)
      total_str: '59.99',
      currency: 'USD',
      charges: [
        {
          name: 'Lock In Annual Subscription',
          amount: 3999,
          payment_plan_name: 'Annual Subscription',
          payment_plan_id: 'plan_annual_001'
        },
        {
          name: 'Friend Bump - Extra Code',
          amount: 2000,
          item_name: 'Friend Bump'
        }
      ],
      future_charges: [
        {
          due: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          frequency: 'year',
          frequency_days: 365,
          amount: 3999
        }
      ]
    },
    product: {
      name: 'Lock In Annual Subscription'
    },
    product_name: 'Lock In Annual Subscription'
  },

  // 3. Annual subscription with tribe bump
  annual_tribe_bump: {
    event: 'order.success',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-ANN-003',
      email: '<EMAIL>',
      first_name: 'Annual',
      last_name: 'Tribe',
      name: 'Annual Tribe'
    },
    order: {
      order_id: 'ORD-ANN-003',
      total: 9999, // $99.99 in cents ($39.99 + $60.00)
      total_str: '99.99',
      currency: 'USD',
      charges: [
        {
          name: 'Lock In Annual Subscription',
          amount: 3999,
          payment_plan_name: 'Annual Subscription',
          payment_plan_id: 'plan_annual_001'
        },
        {
          name: 'Tribe Bump - Multiple Codes',
          amount: 6000,
          item_name: 'Tribe Bump'
        }
      ],
      future_charges: [
        {
          due: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          frequency: 'year',
          frequency_days: 365,
          amount: 3999
        }
      ]
    },
    product: {
      name: 'Lock In Annual Subscription'
    },
    product_name: 'Lock In Annual Subscription'
  },

  // 4. Annual subscription with both friend and tribe bumps
  annual_both_bumps: {
    event: 'order.success',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-ANN-004',
      email: '<EMAIL>',
      first_name: 'Annual',
      last_name: 'Both',
      name: 'Annual Both'
    },
    order: {
      order_id: 'ORD-ANN-004',
      total: 11999, // $119.99 in cents ($39.99 + $20.00 + $60.00)
      total_str: '119.99',
      currency: 'USD',
      charges: [
        {
          name: 'Lock In Annual Subscription',
          amount: 3999,
          payment_plan_name: 'Annual Subscription',
          payment_plan_id: 'plan_annual_001'
        },
        {
          name: 'Friend Bump - Extra Code',
          amount: 2000,
          item_name: 'Friend Bump'
        },
        {
          name: 'Tribe Bump - Multiple Codes',
          amount: 6000,
          item_name: 'Tribe Bump'
        }
      ],
      future_charges: [
        {
          due: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          frequency: 'year',
          frequency_days: 365,
          amount: 3999
        }
      ]
    },
    product: {
      name: 'Lock In Annual Subscription'
    },
    product_name: 'Lock In Annual Subscription'
  },

  // 5. Monthly subscription
  monthly_basic: {
    event: 'order.success',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-MON-001',
      email: '<EMAIL>',
      first_name: 'Monthly',
      last_name: 'User',
      name: 'Monthly User'
    },
    order: {
      order_id: 'ORD-MON-001',
      total: 599, // $5.99 in cents
      total_str: '5.99',
      currency: 'USD',
      charges: [
        {
          name: 'Lock In Monthly Subscription',
          amount: 599,
          payment_plan_name: 'Monthly Subscription',
          payment_plan_id: 'plan_monthly_001'
        }
      ],
      future_charges: [
        {
          due: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          frequency: 'month',
          frequency_days: 30,
          amount: 599
        }
      ]
    },
    product: {
      name: 'Lock In Monthly Subscription'
    },
    product_name: 'Lock In Monthly Subscription'
  },

  // 6. Weekly subscription
  weekly_basic: {
    event: 'order.success',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-WEE-001',
      email: '<EMAIL>',
      first_name: 'Weekly',
      last_name: 'User',
      name: 'Weekly User'
    },
    order: {
      order_id: 'ORD-WEE-001',
      total: 299, // $2.99 in cents
      total_str: '2.99',
      currency: 'USD',
      charges: [
        {
          name: 'Lock In Weekly Subscription',
          amount: 299,
          payment_plan_name: 'Weekly Subscription',
          payment_plan_id: 'plan_weekly_001'
        }
      ],
      future_charges: [
        {
          due: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          frequency: 'week',
          frequency_days: 7,
          amount: 299
        }
      ]
    },
    product: {
      name: 'Lock In Weekly Subscription'
    },
    product_name: 'Lock In Weekly Subscription'
  },

  // 7. Lifetime subscription
  lifetime_basic: {
    event: 'order.success',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-LIF-001',
      email: '<EMAIL>',
      first_name: 'Lifetime',
      last_name: 'User',
      name: 'Lifetime User'
    },
    order: {
      order_id: 'ORD-LIF-001',
      total: 9999, // $99.99 in cents
      total_str: '99.99',
      currency: 'USD',
      charges: [
        {
          name: 'Lock In Lifetime Access',
          amount: 9999,
          payment_plan_name: 'Lifetime Access',
          payment_plan_id: 'plan_lifetime_001'
        }
      ]
      // No future_charges for lifetime
    },
    product: {
      name: 'Lock In Lifetime Access'
    },
    product_name: 'Lock In Lifetime Access'
  },

  // 8. Subscription renewal (annual)
  annual_renewal: {
    event: 'order.rebill_success',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-ANN-001',
      email: '<EMAIL>',
      first_name: 'Annual',
      last_name: 'User',
      name: 'Annual User'
    },
    order: {
      order_id: 'ORD-ANN-REN-001',
      total: 3999, // $39.99 in cents
      total_str: '39.99',
      currency: 'USD',
      charges: [
        {
          name: 'Lock In Annual Subscription',
          amount: 3999,
          payment_plan_name: 'Annual Subscription',
          payment_plan_id: 'plan_annual_001'
        }
      ],
      future_charges: [
        {
          due: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          frequency: 'year',
          frequency_days: 365,
          amount: 3999
        }
      ]
    },
    product: {
      name: 'Lock In Annual Subscription'
    },
    product_name: 'Lock In Annual Subscription'
  },

  // 9. Monthly renewal
  monthly_renewal: {
    event: 'order.rebill_success',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-MON-001',
      email: '<EMAIL>',
      first_name: 'Monthly',
      last_name: 'User',
      name: 'Monthly User'
    },
    order: {
      order_id: 'ORD-MON-REN-001',
      total: 599, // $5.99 in cents
      total_str: '5.99',
      currency: 'USD',
      charges: [
        {
          name: 'Lock In Monthly Subscription',
          amount: 599,
          payment_plan_name: 'Monthly Subscription',
          payment_plan_id: 'plan_monthly_001'
        }
      ],
      future_charges: [
        {
          due: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          frequency: 'month',
          frequency_days: 30,
          amount: 599
        }
      ]
    },
    product: {
      name: 'Lock In Monthly Subscription'
    },
    product_name: 'Lock In Monthly Subscription'
  },

  // 10. Refund
  annual_refund: {
    event: 'order.refund',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-ANN-001',
      email: '<EMAIL>',
      first_name: 'Annual',
      last_name: 'User',
      name: 'Annual User'
    },
    order: {
      order_id: 'ORD-ANN-REF-001',
      total: 3999, // $39.99 in cents
      total_str: '39.99',
      currency: 'USD'
    },
    product: {
      name: 'Lock In Annual Subscription'
    },
    product_name: 'Lock In Annual Subscription'
  },

  // 11. Subscription cancellation
  annual_cancellation: {
    event: 'order.subscription_cancelled',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-ANN-001',
      email: '<EMAIL>',
      first_name: 'Annual',
      last_name: 'User',
      name: 'Annual User'
    }
  },

  // 12. Subscription resumption
  annual_resumption: {
    event: 'order.subscription_resumed',
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: 'CUST-ANN-001',
      email: '<EMAIL>',
      first_name: 'Annual',
      last_name: 'User',
      name: 'Annual User'
    }
  }
};

// Helper function to generate test payload with custom data
function generateTestPayload(productType, options = {}) {
  const {
    email = `${productType}@test.com`,
    includeFriendBump = false,
    includeTribeBump = false,
    eventType = 'order.success'
  } = options;

  const products = {
    annual: { price: 39.99, name: 'Lock In Annual Subscription' },
    monthly: { price: 5.99, name: 'Lock In Monthly Subscription' },
    weekly: { price: 2.99, name: 'Lock In Weekly Subscription' },
    lifetime: { price: 99.99, name: 'Lock In Lifetime Access' }
  };

  const product = products[productType];
  if (!product) {
    throw new Error(`Invalid product type: ${productType}`);
  }

  // Build charges array
  const charges = [{
    name: product.name,
    amount: product.price * 100,
    payment_plan_name: product.name.replace('Lock In ', ''),
    payment_plan_id: `plan_${productType}_001`
  }];

  let totalAmount = product.price;

  // Add bumps if specified
  if (includeFriendBump && productType === 'annual') {
    charges.push({
      name: 'Friend Bump - Extra Code',
      amount: 2000,
      item_name: 'Friend Bump'
    });
    totalAmount += 20.00;
  }

  if (includeTribeBump && productType === 'annual') {
    charges.push({
      name: 'Tribe Bump - Multiple Codes',
      amount: 6000,
      item_name: 'Tribe Bump'
    });
    totalAmount += 60.00;
  }

  const payload = {
    event: eventType,
    thrivecart_secret: process.env.TEST_WEBHOOK_SECRET || 'test-secret-' + Date.now(),
    customer: {
      customer_id: `CUST-${productType.toUpperCase()}-${Date.now()}`,
      email: email,
      first_name: productType.charAt(0).toUpperCase() + productType.slice(1),
      last_name: 'User',
      name: `${productType.charAt(0).toUpperCase() + productType.slice(1)} User`
    },
    order: {
      order_id: `ORD-${productType.toUpperCase()}-${Date.now()}`,
      total: Math.round(totalAmount * 100), // Convert to cents
      total_str: totalAmount.toFixed(2),
      currency: 'USD',
      charges: charges
    },
    product: {
      name: product.name
    },
    product_name: product.name
  };

  // Add future charges for subscriptions (not lifetime)
  if (productType !== 'lifetime' && eventType.includes('success')) {
    const frequencyDays = {
      annual: 365,
      monthly: 30,
      weekly: 7
    };

    const frequency = {
      annual: 'year',
      monthly: 'month',
      weekly: 'week'
    };

    payload.order.future_charges = [{
      due: new Date(Date.now() + frequencyDays[productType] * 24 * 60 * 60 * 1000).toISOString(),
      frequency: frequency[productType],
      frequency_days: frequencyDays[productType],
      amount: product.price * 100
    }];
  }

  return payload;
}

// Scenario descriptions for testing
const scenarios = [
  { name: 'Annual Basic', payload: testPayloads.annual_basic, expectedCodes: 1 },
  { name: 'Annual with Friend Bump', payload: testPayloads.annual_friend_bump, expectedCodes: 2 },
  { name: 'Annual with Tribe Bump', payload: testPayloads.annual_tribe_bump, expectedCodes: 4 },
  { name: 'Annual with Both Bumps', payload: testPayloads.annual_both_bumps, expectedCodes: 5 },
  { name: 'Monthly Basic', payload: testPayloads.monthly_basic, expectedCodes: 1 },
  { name: 'Weekly Basic', payload: testPayloads.weekly_basic, expectedCodes: 1 },
  { name: 'Lifetime Basic', payload: testPayloads.lifetime_basic, expectedCodes: 1 },
  { name: 'Annual Renewal', payload: testPayloads.annual_renewal, expectedCodes: 0 },
  { name: 'Monthly Renewal', payload: testPayloads.monthly_renewal, expectedCodes: 0 },
  { name: 'Annual Refund', payload: testPayloads.annual_refund, expectedCodes: 0 },
  { name: 'Annual Cancellation', payload: testPayloads.annual_cancellation, expectedCodes: 0 },
  { name: 'Annual Resumption', payload: testPayloads.annual_resumption, expectedCodes: 0 }
];

module.exports = {
  testPayloads,
  generateTestPayload,
  scenarios
};