-- Migration to remove FastSpring columns and consolidate to ThriveCart only
-- Run this migration after deploying the new code

-- Rename FastSpring columns to ThriveCart in paid_users table
ALTER TABLE paid_users 
  RENAME COLUMN fastspring_order_id TO thrivecart_order_id;

ALTER TABLE paid_users 
  RENAME COLUMN fastspring_subscription_id TO thrivecart_subscription_id;

-- Rename FastSpring columns in payment_transactions table
ALTER TABLE payment_transactions 
  RENAME COLUMN fastspring_order_id TO provider_order_id;

ALTER TABLE payment_transactions 
  RENAME COLUMN fastspring_reference TO provider_reference;

-- Update default source in webhook_events
ALTER TABLE webhook_events 
  ALTER COLUMN source SET DEFAULT 'thrivecart';

-- Update existing FastSpring webhook events to indicate they are from the legacy provider
UPDATE webhook_events 
  SET source = 'fastspring_legacy' 
  WHERE source = 'fastspring';

-- Add comment to tables for documentation
COMMENT ON COLUMN paid_users.thrivecart_order_id IS 'Order ID from ThriveCart payment provider';
COMMENT ON COLUMN paid_users.thrivecart_subscription_id IS 'Subscription ID from ThriveCart payment provider';
COMMENT ON COLUMN payment_transactions.provider_order_id IS 'Order ID from payment provider (ThriveCart)';
COMMENT ON COLUMN payment_transactions.provider_reference IS 'Reference from payment provider (ThriveCart)';

-- Log migration completion
INSERT INTO webhook_events (event_type, payload, source, processed, processed_at)
VALUES ('migration', 
        '{"type": "remove_fastspring", "timestamp": "' || NOW() || '"}',
        'system',
        true,
        NOW());