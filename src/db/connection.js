/**
 * Database Connection Pool
 * This module re-exports the optimized pool from connectionPool.js
 * while maintaining backward compatibility
 */

const { pool, healthCheck, executeWithRetry, getPoolStats } = require('./connectionPool');

// Export the pool as default for backward compatibility
module.exports = pool;

// Also export enhanced features
module.exports.pool = pool;
module.exports.healthCheck = healthCheck;
module.exports.executeWithRetry = executeWithRetry;
module.exports.getPoolStats = getPoolStats;