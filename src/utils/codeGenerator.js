const crypto = require('crypto');
const pool = require('../db/connection');

/**
 * Generate a unique access code in format HABIT-XXXXXX
 */
async function generateAccessCode() {
  let code;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  while (!isUnique && attempts < maxAttempts) {
    // Increase entropy: Generate 6 random bytes instead of 3
    const randomBytes = crypto.randomBytes(6);
    // Convert to base64 and remove special chars, then take first 6 chars
    const randomPart = randomBytes.toString('base64')
      .replace(/[+/=]/g, '') // Remove special chars
      .substring(0, 6)
      .toUpperCase();
    
    code = `HABIT-${randomPart}`;

    // Check if code already exists (collision detection)
    const result = await pool.query(
      'SELECT id FROM paid_users WHERE access_code = $1',
      [code]
    );

    if (result.rows.length === 0) {
      isUnique = true;
    } else {
      // Log collision for monitoring
      console.log('Access code collision detected, generating new code');
    }

    attempts++;
  }

  if (!isUnique) {
    throw new Error('Failed to generate unique access code after ' + maxAttempts + ' attempts');
  }

  return code;
}

/**
 * Generate a unique affiliate code in format AFF-XXXXXX
 */
async function generateAffiliateCode() {
  let code;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  while (!isUnique && attempts < maxAttempts) {
    // Increase entropy: Generate 6 random bytes instead of 3
    const randomBytes = crypto.randomBytes(6);
    // Convert to base64 and remove special chars, then take first 6 chars
    const randomPart = randomBytes.toString('base64')
      .replace(/[+/=]/g, '') // Remove special chars
      .substring(0, 6)
      .toUpperCase();
    
    code = `AFF-${randomPart}`;

    // Check if code already exists (collision detection)
    const result = await pool.query(
      'SELECT id FROM paid_users WHERE affiliate_code = $1',
      [code]
    );

    if (result.rows.length === 0) {
      isUnique = true;
    } else {
      // Log collision for monitoring
      console.log('Affiliate code collision detected, generating new code');
    }

    attempts++;
  }

  if (!isUnique) {
    throw new Error('Failed to generate unique affiliate code after ' + maxAttempts + ' attempts');
  }

  return code;
}

/**
 * Validate access code format
 */
function validateAccessCode(code) {
  if (!code || typeof code !== 'string') {
    return false;
  }

  // Check format: HABIT-XXXXXX (6 alphanumeric characters after HABIT-)
  const pattern = /^HABIT-[A-Z0-9]{6}$/;
  return pattern.test(code.toUpperCase());
}

/**
 * Validate affiliate code format
 */
function validateAffiliateCode(code) {
  if (!code || typeof code !== 'string') {
    return false;
  }

  // Check format: AFF-XXXXXX (6 alphanumeric characters after AFF-)
  const pattern = /^AFF-[A-Z0-9]{6}$/;
  return pattern.test(code.toUpperCase());
}

module.exports = {
  generateAccessCode,
  generateAffiliateCode,
  validateAccessCode,
  validateAffiliateCode
};