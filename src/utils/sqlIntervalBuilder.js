const logger = require('../config/logger');

/**
 * Utility for building safe SQL interval expressions
 * Prevents SQL injection by validating and sanitizing interval parameters
 */

class SqlIntervalBuilder {
  constructor() {
    this.validUnits = ['second', 'minute', 'hour', 'day', 'week', 'month', 'year'];
    this.pluralMap = {
      'second': 'seconds',
      'minute': 'minutes',
      'hour': 'hours',
      'day': 'days',
      'week': 'weeks',
      'month': 'months',
      'year': 'years'
    };
  }

  /**
   * Build a safe SQL interval string
   * @param {number} value - The numeric value for the interval
   * @param {string} unit - The unit of time (day, hour, minute, etc.)
   * @returns {string} - Safe interval string for SQL queries
   */
  buildSafeInterval(value, unit) {
    // Validate and sanitize value
    const numericValue = parseInt(value, 10);
    if (isNaN(numericValue) || numericValue < 0) {
      throw new Error(`Invalid interval value: ${value}`);
    }

    // Normalize unit to lowercase
    const normalizedUnit = unit.toLowerCase().replace(/s$/, ''); // Remove trailing 's' if present
    
    // Validate unit
    if (!this.validUnits.includes(normalizedUnit)) {
      throw new Error(`Invalid interval unit: ${unit}. Valid units are: ${this.validUnits.join(', ')}`);
    }

    // Build the interval string
    const pluralUnit = numericValue === 1 ? normalizedUnit : this.pluralMap[normalizedUnit];
    return `${numericValue} ${pluralUnit}`;
  }

  /**
   * Build a PostgreSQL make_interval function call
   * This is the safest approach as it uses parameterized values
   * @param {number} value - The numeric value
   * @param {string} unit - The unit of time
   * @returns {object} - Object with query fragment and parameter
   */
  buildMakeInterval(value, unit) {
    // Validate value
    const numericValue = parseInt(value, 10);
    if (isNaN(numericValue) || numericValue < 0) {
      throw new Error(`Invalid interval value: ${value}`);
    }

    // Normalize unit
    const normalizedUnit = unit.toLowerCase().replace(/s$/, '');
    
    // Map to PostgreSQL make_interval parameters
    const intervalParams = {
      'year': 'years',
      'month': 'months',
      'week': 'weeks',
      'day': 'days',
      'hour': 'hours',
      'minute': 'mins',  // Note: PostgreSQL uses 'mins' not 'minutes'
      'second': 'secs'   // Note: PostgreSQL uses 'secs' not 'seconds'
    };

    if (!intervalParams[normalizedUnit]) {
      throw new Error(`Invalid interval unit for make_interval: ${unit}`);
    }

    const paramName = intervalParams[normalizedUnit];
    
    return {
      query: `make_interval(${paramName} => $1)`,
      value: numericValue
    };
  }

  /**
   * Convert common retention policy strings to safe intervals
   * @param {string} policyString - String like "30 days", "1 year", etc.
   * @returns {object} - Object with query fragment and parameters
   */
  parseRetentionPolicy(policyString) {
    // Parse strings like "30 days", "1 year", "6 months"
    const match = policyString.match(/^(\d+)\s+(\w+)$/);
    if (!match) {
      throw new Error(`Invalid retention policy format: ${policyString}`);
    }

    const [, value, unit] = match;
    return this.buildMakeInterval(value, unit);
  }

  /**
   * Build a safe WHERE clause for date comparisons
   * @param {string} column - The column name to compare
   * @param {string} operator - Comparison operator (<, >, <=, >=)
   * @param {number} value - The interval value
   * @param {string} unit - The interval unit
   * @returns {object} - Object with query and parameter
   */
  buildDateComparison(column, operator, value, unit) {
    // Validate column name (basic SQL identifier validation)
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(column)) {
      throw new Error(`Invalid column name: ${column}`);
    }

    // Validate operator
    const validOperators = ['<', '>', '<=', '>=', '=', '!='];
    if (!validOperators.includes(operator)) {
      throw new Error(`Invalid operator: ${operator}`);
    }

    const interval = this.buildMakeInterval(value, unit);
    
    return {
      query: `${column} ${operator} NOW() - ${interval.query}`,
      value: interval.value
    };
  }

  /**
   * Helper to migrate old INTERVAL syntax to parameterized queries
   * @param {string} oldQuery - Query with INTERVAL '30 days' syntax
   * @returns {object} - Object with new query and parameters array
   */
  migrateIntervalQuery(oldQuery) {
    const intervalPattern = /INTERVAL\s+'(\d+)\s+(\w+)'/gi;
    const parameters = [];
    let paramIndex = 1;
    
    const newQuery = oldQuery.replace(intervalPattern, (match, value, unit) => {
      try {
        const interval = this.buildMakeInterval(value, unit);
        parameters.push(interval.value);
        const placeholder = interval.query.replace('$1', `$${paramIndex}`);
        paramIndex++;
        return placeholder;
      } catch (error) {
        logger.warn('Failed to migrate interval in query', { match, error: error.message });
        return match; // Keep original if migration fails
      }
    });

    return {
      query: newQuery,
      parameters
    };
  }

  /**
   * Validate if a string could be used as a safe interval
   * @param {string} intervalString - The interval string to validate
   * @returns {boolean} - True if valid, false otherwise
   */
  isValidInterval(intervalString) {
    try {
      const match = intervalString.match(/^(\d+)\s+(\w+)$/);
      if (!match) return false;
      
      const [, value, unit] = match;
      this.buildSafeInterval(value, unit);
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
module.exports = new SqlIntervalBuilder();

// Also export class for testing
module.exports.SqlIntervalBuilder = SqlIntervalBuilder;