const crypto = require('crypto');
const logger = require('../config/logger');

// Get PII salt from environment or generate a stable one
const PII_SALT = process.env.PII_SALT || 'lockin-pii-hash-salt-2025';

/**
 * Hash PII data for logging purposes
 * @param {string} data - The PII data to hash
 * @param {string} type - Type of PII (phone, email, name, etc.)
 * @returns {string} Hashed identifier
 */
function hashPII(data, type = 'unknown') {
  if (!data) return `${type}_unknown`;
  
  try {
    const hash = crypto
      .createHash('sha256')
      .update(String(data) + PII_SALT)
      .digest('hex');
    
    // Return type prefix with first 8 chars of hash for identification
    return `${type}_${hash.substring(0, 8)}`;
  } catch (error) {
    logger.error('Error hashing PII', { error: error.message });
    return `${type}_error`;
  }
}

/**
 * Hash phone number for logging
 * @param {string} phone - Phone number to hash
 * @returns {string} Hashed phone identifier
 */
function hashPhone(phone) {
  if (!phone) return 'phone_unknown';
  
  // Remove WhatsApp prefix if present
  const cleanPhone = phone.replace('whatsapp:', '');
  return hashPII(cleanPhone, 'phone');
}

/**
 * Hash email address for logging
 * @param {string} email - Email address to hash
 * @returns {string} Hashed email identifier
 */
function hashEmail(email) {
  if (!email) return 'email_unknown';
  
  // Normalize email to lowercase
  const normalizedEmail = email.toLowerCase().trim();
  return hashPII(normalizedEmail, 'email');
}

/**
 * Hash customer name for logging
 * @param {string} name - Customer name to hash
 * @returns {string} Hashed name identifier
 */
function hashName(name) {
  if (!name) return 'name_unknown';
  
  // Normalize name
  const normalizedName = name.trim().toLowerCase();
  return hashPII(normalizedName, 'name');
}

/**
 * Hash IP address for logging
 * @param {string} ip - IP address to hash
 * @returns {string} Hashed IP identifier
 */
function hashIP(ip) {
  if (!ip) return 'ip_unknown';
  
  return hashPII(ip, 'ip');
}

/**
 * Mask phone number for display (shows last 4 digits)
 * @param {string} phone - Phone number to mask
 * @returns {string} Masked phone number
 */
function maskPhone(phone) {
  if (!phone || phone.length < 4) return '****';
  
  const clean = phone.replace('whatsapp:', '');
  const last4 = clean.slice(-4);
  return `***${last4}`;
}

/**
 * Mask email address for display
 * @param {string} email - Email address to mask
 * @returns {string} Masked email address
 */
function maskEmail(email) {
  if (!email || !email.includes('@')) return '***@***.***';
  
  const [localPart, domain] = email.split('@');
  
  // Show first letter and mask rest of local part
  const maskedLocal = localPart.length > 0
    ? localPart[0] + '***'
    : '***';
  
  // Show first part of domain
  const domainParts = domain.split('.');
  const maskedDomain = domainParts.length > 0
    ? domainParts[0].substring(0, 3) + '***.***'
    : '***.***';
  
  return `${maskedLocal}@${maskedDomain}`;
}

/**
 * Create a safe log object with PII hashed
 * @param {Object} data - Data object potentially containing PII
 * @returns {Object} Safe object for logging
 */
function createSafeLogObject(data) {
  if (!data || typeof data !== 'object') return data;
  
  const safe = {};
  
  for (const [key, value] of Object.entries(data)) {
    const lowerKey = key.toLowerCase();
    
    // Hash PII fields
    if (lowerKey.includes('phone') || lowerKey === 'from' || lowerKey === 'to') {
      safe[key] = hashPhone(value);
      safe[`${key}_masked`] = maskPhone(value);
    } else if (lowerKey.includes('email')) {
      safe[key] = hashEmail(value);
      safe[`${key}_masked`] = maskEmail(value);
    } else if (lowerKey.includes('name') && !lowerKey.includes('product')) {
      safe[key] = hashName(value);
    } else if (lowerKey === 'ip' || lowerKey.includes('address')) {
      safe[key] = hashIP(value);
    } else if (lowerKey.includes('password') || lowerKey.includes('secret') || 
               lowerKey.includes('token') || lowerKey.includes('key')) {
      safe[key] = '[REDACTED]';
    } else if (typeof value === 'object' && value !== null) {
      // Recursively handle nested objects
      safe[key] = createSafeLogObject(value);
    } else {
      // Keep non-PII data as is
      safe[key] = value;
    }
  }
  
  return safe;
}

/**
 * Create audit log entry without PII
 * @param {string} action - Action being performed
 * @param {Object} data - Data related to the action
 * @returns {Object} Audit log entry
 */
function createAuditLog(action, data = {}) {
  return {
    timestamp: new Date().toISOString(),
    action,
    data: createSafeLogObject(data),
    // Add request ID if available for tracing
    requestId: data.requestId || crypto.randomBytes(8).toString('hex')
  };
}

/**
 * Check if a hash matches a PII value
 * @param {string} hash - The hash to check
 * @param {string} value - The value to compare
 * @param {string} type - Type of PII
 * @returns {boolean} Whether the hash matches
 */
function verifyPIIHash(hash, value, type = 'unknown') {
  const expectedHash = hashPII(value, type);
  return hash === expectedHash;
}

module.exports = {
  hashPII,
  hashPhone,
  hashEmail,
  hashName,
  hashIP,
  maskPhone,
  maskEmail,
  createSafeLogObject,
  createAuditLog,
  verifyPIIHash
};