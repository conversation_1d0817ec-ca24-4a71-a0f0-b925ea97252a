module.exports = {
  // User States
  USER_STATUS: {
    LOCKED: 'LOCKED',
    ONBOARDING: 'ONBOARDING',
    ACTIVE: 'ACTIVE',
    PAUSED: 'PAUSED'
  },
  
  // Session States
  STATES: {
    MAIN_MENU: 'MAIN_MENU',
    AWAITING_NAME: 'AWAITING_NAME',
    AWAITING_TIMEZONE: 'AWAITING_TIMEZONE',
    ONBOARDING_MENU: 'ONBOARDING_MENU',
    SETTING_HABIT: 'SETTING_HABIT',
    LOGGING_HABITS: 'LOGGING_HABITS',
    VIEWING_PROGRESS: 'VIEWING_PROGRESS',
    STATS_MENU: 'STATS_MENU',
    STATS_30_DAY: 'STATS_30_DAY',
    STATS_100_DAY: 'STATS_100_DAY',
    SETTINGS_MENU: 'SETTINGS_MENU',
    COMPLETION_SCREEN: 'COMPLETION_SCREEN'
  },
  
  // Session timeout (30 minutes)
  SESSION_TIMEOUT_MS: 30 * 60 * 1000,
  
  // Rate limiting
  RATE_LIMIT: {
    WINDOW_MS: 5 * 60 * 1000, // 5 minutes (was 15)
    MAX_REQUESTS: 20, // 20 requests (was 100)
    MESSAGE: 'Too many requests. Please try again later.',
    SKIP_SUCCESSFUL_REQUESTS: false, // Count all requests
    PROGRESSIVE_DELAY: true // Add progressive penalties
  },
  
  // WhatsApp Business API compliance
  WHATSAPP: {
    MESSAGE_WINDOW_HOURS: 24,
    MAX_MESSAGE_LENGTH: 4096
  },
  
  // Habit constraints
  HABITS: {
    MIN_NUMBER: 1,
    MAX_NUMBER: 5,
    MAX_NAME_LENGTH: 100
  },
  
  // Compliance keywords
  STOP_KEYWORDS: ['STOP', 'UNSUBSCRIBE', 'QUIT', 'CANCEL', 'END', 'OPTOUT', 'OPT OUT', 'OPT-OUT'],
  
  // Audit event types
  AUDIT_EVENTS: {
    USER_CREATED: 'USER_CREATED',
    USER_OPTED_OUT: 'USER_OPTED_OUT',
    USER_OPTED_IN: 'USER_OPTED_IN',
    HABIT_CREATED: 'HABIT_CREATED',
    HABIT_UPDATED: 'HABIT_UPDATED',
    HABIT_LOGGED: 'HABIT_LOGGED',
    SESSION_TIMEOUT: 'SESSION_TIMEOUT',
    RATE_LIMITED: 'RATE_LIMITED',
    ERROR: 'ERROR',
    ADMIN_ACCESS: 'ADMIN_ACCESS',
    ADMIN_LOGIN: 'ADMIN_LOGIN',
    API_KEY_ACCESS: 'API_KEY_ACCESS',
    USER_UNLOCKED: 'USER_UNLOCKED',
    INVALID_ACCESS_CODE: 'INVALID_ACCESS_CODE',
    PAYMENT_REQUIRED: 'PAYMENT_REQUIRED',
    MESSAGE_RECEIVED: 'MESSAGE_RECEIVED',
    MESSAGE_WINDOW_EXPIRED: 'MESSAGE_WINDOW_EXPIRED',
    DATA_DELETION_REQUESTED: 'DATA_DELETION_REQUESTED',
    DATA_EXPORTED: 'DATA_EXPORTED'
  }
};