const path = require('path');

// Production Configuration Module
module.exports = {
    // Server Configuration
    server: {
        port: process.env.PORT || 3000,
        host: process.env.HOST || '0.0.0.0',
        trustProxy: true,
        corsOrigin: process.env.CORS_ORIGIN || false,
    },

    // Database Configuration
    database: {
        connectionString: process.env.DATABASE_URL,
        ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
        pool: {
            min: parseInt(process.env.DB_POOL_MIN || '2'),
            max: parseInt(process.env.DB_POOL_MAX || '10'),
            idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT_MILLIS || '30000'),
            connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT_MILLIS || '2000'),
        },
        statementTimeout: 30000,
        query_timeout: 30000,
    },

    // Redis Cache Configuration
    redis: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        enabled: process.env.CACHE_ENABLED === 'true',
        ttl: parseInt(process.env.CACHE_TTL_SECONDS || '3600'),
        maxRetriesPerRequest: 3,
        retryStrategy: (times) => Math.min(times * 50, 2000),
    },

    // Security Configuration
    security: {
        jwtSecret: process.env.JWT_SECRET,
        jwtExpiry: process.env.JWT_EXPIRY || '7d',
        sessionSecret: process.env.SESSION_SECRET,
        cookieSecure: process.env.COOKIE_SECURE === 'true',
        bcryptRounds: 12,
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15 minutes
    },

    // Rate Limiting
    rateLimiting: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
        max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
        skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS === 'true',
        standardHeaders: true,
        legacyHeaders: false,
        message: 'Too many requests, please try again later.',
    },

    // Logging Configuration
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        dir: process.env.LOG_DIR || '/var/log/habittracker',
        maxSize: process.env.LOG_MAX_SIZE || '10m',
        maxFiles: process.env.LOG_MAX_FILES || '14d',
        compress: process.env.LOG_COMPRESS === 'true',
        auditEnabled: process.env.AUDIT_LOG_ENABLED === 'true',
        format: 'json',
        handleExceptions: true,
        handleRejections: true,
    },

    // Email Configuration
    email: {
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_PORT === '465',
        auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
        },
        from: process.env.EMAIL_FROM,
        queueEnabled: process.env.EMAIL_QUEUE_ENABLED === 'true',
        batchSize: parseInt(process.env.EMAIL_BATCH_SIZE || '10'),
        retryAttempts: 3,
        retryDelay: 5000,
    },

    // Payment Configuration
    payment: {
        testMode: process.env.PAYMENT_TEST_MODE === 'true',
        thriveCartSecret: process.env.THRIVECART_SECRET,
        retryAttempts: parseInt(process.env.PAYMENT_RETRY_ATTEMPTS || '3'),
        timeout: parseInt(process.env.PAYMENT_TIMEOUT_MS || '30000'),
        webhookUrl: process.env.THRIVECART_WEBHOOK_URL,
    },

    // Compliance Configuration
    compliance: {
        gdprEnabled: process.env.GDPR_ENABLED === 'true',
        dataRetentionDays: parseInt(process.env.DATA_RETENTION_DAYS || '365'),
        auditLogRetentionDays: parseInt(process.env.AUDIT_LOG_RETENTION_DAYS || '730'),
        piiEncryption: process.env.PII_ENCRYPTION_ENABLED === 'true',
        cookieConsent: true,
        privacyPolicyUrl: '/privacy',
        termsOfServiceUrl: '/terms',
    },

    // Performance & Monitoring
    monitoring: {
        enabled: process.env.ENABLE_PERFORMANCE_MONITORING === 'true',
        healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000'),
        metricsEnabled: process.env.METRICS_ENABLED === 'true',
        apmEnabled: false, // Can be enabled with APM service
    },

    // Feature Flags
    features: {
        payments: process.env.FEATURE_PAYMENTS === 'true',
        emailNotifications: process.env.FEATURE_EMAIL_NOTIFICATIONS === 'true',
        smsReminders: process.env.FEATURE_SMS_REMINDERS === 'true',
        analytics: process.env.FEATURE_ANALYTICS === 'true',
        maintenanceMode: process.env.MAINTENANCE_MODE === 'true',
    },

    // Backup Configuration
    backup: {
        enabled: process.env.BACKUP_ENABLED === 'true',
        schedule: process.env.BACKUP_SCHEDULE || '0 2 * * *',
        retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS || '30'),
        location: path.join(__dirname, '../../backups'),
    },

    // Twilio Configuration
    twilio: {
        accountSid: process.env.TWILIO_ACCOUNT_SID,
        authToken: process.env.TWILIO_AUTH_TOKEN,
        phoneNumber: process.env.TWILIO_PHONE_NUMBER,
        webhookUrl: process.env.TWILIO_WEBHOOK_URL,
        statusCallbackUrl: process.env.TWILIO_STATUS_CALLBACK_URL,
        maxRetries: 3,
        timeout: 30000,
    },

    // Application Settings
    app: {
        name: 'LockIn Habit Tracker',
        version: require('../../package.json').version,
        environment: 'production',
        timezone: 'UTC',
        locale: 'en-US',
        maxHabitsPerUser: 5,
        sessionTimeout: 30 * 60 * 1000, // 30 minutes
        adminAccess: process.env.ADMIN_ACCESS === 'true',
        adminDashboard: process.env.ADMIN_DASHBOARD_ENABLED === 'true',
    },
};