# User Guide

**Lock In - WhatsApp Habit Tracker Bot**  
**Your Personal Habit Tracking Assistant**

---

## Table of Contents

- [Getting Started](#getting-started)
- [Account Activation](#account-activation)
- [Setting Up Your Habits](#setting-up-your-habits)
- [Daily Habit Tracking](#daily-habit-tracking)
- [Viewing Your Progress](#viewing-your-progress)
- [Managing Your Account](#managing-your-account)
- [Commands Reference](#commands-reference)
- [Troubleshooting](#troubleshooting)

---

## Getting Started

### What is Lock In?

Lock In is a WhatsApp-based habit tracker that helps you build consistent daily habits. Track up to 5 habits, view your progress, and stay motivated with streak tracking and analytics.

### How It Works

1. **Purchase a subscription** from our website
2. **Receive an access code** via email
3. **Activate your account** on WhatsApp
4. **Set up your habits** (1-5 habits)
5. **Track daily** and view your progress

### Subscription Plans

| Plan | Price | Duration | Best For |
|------|-------|----------|----------|
| **Weekly** | $2.99 | 7 days | Trying it out |
| **Monthly** | $5.99 | 30 days | Regular users |
| **Yearly** | $39.99 | 365 days | Committed users (best value) |
| **Lifetime** | $99.00 | Forever | Long-term commitment |

---

## Account Activation

### Step 1: Purchase Subscription

Visit [lockintracker.com](https://lockintracker.com) and choose your subscription plan.

### Step 2: Check Your Email

After purchase, you'll receive a welcome email with:
- Your unique access code (format: `HABIT-XXXXX`)
- Setup instructions
- Bot phone number

### Step 3: Start WhatsApp Conversation

1. **Open WhatsApp** on your phone
2. **Send a message** to the bot number provided in your welcome email
3. **Type exactly**: `START HABIT-XXXXX` (replace with your code)

### Step 4: Account Activated!

You'll receive a welcome message confirming your account is active.

---

## Setting Up Your Habits

### Initial Setup Process

After activation, you'll be guided through setup:

#### 1. Set Your Display Name
```
Bot: What would you like me to call you?
You: John
Bot: Nice to meet you, John!
```

#### 2. Choose Your Timezone
```
Bot: What's your timezone? (e.g., America/New_York, Europe/London)
You: America/New_York
Bot: Timezone set to America/New_York
```

#### 3. Add Your Habits

You can track 1-5 daily habits. Examples:
- 🏃‍♂️ Exercise for 30 minutes
- 📚 Read for 20 minutes  
- 🧘‍♀️ Meditate for 10 minutes
- 💧 Drink 8 glasses of water
- 📱 No social media before noon

```
Bot: Let's set up your habits! What's habit #1?
You: Exercise for 30 minutes
Bot: Great! Habit #1: Exercise for 30 minutes

Bot: What's habit #2? (or type 'done' if finished)
You: Read for 20 minutes
Bot: Habit #2: Read for 20 minutes
```

### Habit Guidelines

- **Be Specific**: "Exercise for 30 minutes" vs "Exercise"
- **Make it Measurable**: Include time, quantity, or specific actions
- **Keep it Simple**: One clear action per habit
- **Be Realistic**: Start with achievable goals

---

## Daily Habit Tracking

### Daily Check-in Process

Each day, you can log your habits:

#### 1. Start Daily Check-in
```
You: menu
Bot: 📋 MAIN MENU
1️⃣ Log today's habits
2️⃣ View progress
3️⃣ Statistics
4️⃣ Settings

You: 1
```

#### 2. Log Each Habit
```
Bot: 🏃‍♂️ Exercise for 30 minutes
✅ Completed  ❌ Not completed

You: ✅
Bot: Great job! Habit logged.

Bot: 📚 Read for 20 minutes  
✅ Completed  ❌ Not completed

You: ❌
Bot: No worries, tomorrow is a new day!
```

#### 3. View Daily Summary
After logging all habits, you'll see:
```
🎉 DAILY SUMMARY - January 28, 2025

✅ Exercise for 30 minutes
❌ Read for 20 minutes
✅ Meditate for 10 minutes

📊 Today: 2/3 habits completed (67%)
🔥 Current streak: 5 days
```

### Quick Logging Tips

- **Use emojis**: ✅ for completed, ❌ for not completed
- **Type numbers**: `1` for completed, `2` for not completed
- **Be honest**: Accurate logging helps track real progress
- **Log daily**: Consistency in tracking builds the habit of tracking

---

## Viewing Your Progress

### Progress Reports

Access different timeframes of your progress:

#### 7-Day Progress
```
You: 2 (from main menu)
Bot: 📊 7-DAY PROGRESS

🏃‍♂️ Exercise: 5/7 days (71%)
📚 Reading: 4/7 days (57%)
🧘‍♀️ Meditation: 6/7 days (86%)

Overall: 15/21 total (71%)
```

#### Statistics Menu
```
You: 3 (from main menu)
Bot: 📈 STATISTICS MENU
1️⃣ 30-day report
2️⃣ 100-day report
3️⃣ Back to menu

You: 1
Bot: 📊 30-DAY PROGRESS REPORT
[Detailed 30-day breakdown]
```

### Understanding Your Stats

- **Completion Rate**: Percentage of days you completed each habit
- **Current Streak**: Consecutive days completing ALL habits
- **Best Streak**: Your longest streak ever
- **Total Days**: Days you've been tracking

### Progress Visualization

Your progress includes:
- 📊 **Bar charts** showing completion rates
- 🔥 **Streak counters** for motivation
- 📈 **Trend analysis** over time
- 🎯 **Goal tracking** and achievements

---

## Managing Your Account

### Settings Menu

Access account settings:
```
You: 4 (from main menu)
Bot: ⚙️ SETTINGS MENU
1️⃣ Edit habits
2️⃣ Change timezone
3️⃣ Account info
4️⃣ Back to menu
```

### Editing Habits

#### Add New Habits
```
You: 1 (edit habits)
Bot: Current habits:
1️⃣ Exercise for 30 minutes
2️⃣ Read for 20 minutes

Which habit to edit? (1-5 or 'add')
You: add
Bot: What's your new habit?
You: Drink 8 glasses of water
```

#### Modify Existing Habits
```
You: 1 (edit habits)
You: 2 (edit habit #2)
Bot: Current: Read for 20 minutes
New habit name:
You: Read for 30 minutes
Bot: Updated! Habit #2: Read for 30 minutes
```

### Changing Timezone

```
You: 2 (change timezone)
Bot: Current timezone: America/New_York
New timezone:
You: Europe/London
Bot: Timezone updated to Europe/London
```

### Account Information

View your subscription and account details:
```
You: 3 (account info)
Bot: 👤 ACCOUNT INFO
Name: John
Timezone: America/New_York
Subscription: Monthly (expires Feb 28, 2025)
Member since: January 1, 2025
```

---

## Commands Reference

### Basic Commands

| Command | Description | Example |
|---------|-------------|---------|
| `menu` | Return to main menu | `menu` |
| `START HABIT-XXXXX` | Activate account | `START HABIT-ABC123` |
| `STOP` | Opt out of messages | `STOP` |
| `START` | Resubscribe after opt-out | `START` |

### Menu Navigation

| Number | Action | Context |
|--------|--------|---------|
| `1` | Log today's habits | Main menu |
| `2` | View progress | Main menu |
| `3` | Statistics | Main menu |
| `4` | Settings | Main menu |
| `1` | 30-day report | Statistics menu |
| `2` | 100-day report | Statistics menu |

### Habit Logging

| Input | Meaning |
|-------|---------|
| `✅` or `1` | Habit completed |
| `❌` or `2` | Habit not completed |

### Special Commands

| Command | Description | When to Use |
|---------|-------------|-------------|
| `RESET_TEST` | Reset account (dev only) | Testing/development |
| `help` | Get help message | When confused |

---

## Troubleshooting

### Common Issues

#### "Access code not found"
- **Check spelling**: Ensure exact format `START HABIT-XXXXX`
- **Check email**: Look for the welcome email with your code
- **Contact support**: If code still doesn't work

#### "Session timed out"
- **Type `menu`**: Restart your session
- **Wait 30 minutes**: Sessions auto-expire for security

#### "Not receiving messages"
- **Check WhatsApp**: Ensure messages aren't blocked
- **Check number**: Verify you're messaging the correct bot number
- **Try `START`**: Resubscribe if you accidentally opted out

#### "Wrong timezone"
- **Go to settings**: Menu → 4 → 2
- **Use proper format**: `America/New_York`, `Europe/London`
- **Check list**: [timezone list](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)

### Getting Help

#### Self-Help
1. **Type `menu`** to restart
2. **Check this guide** for common solutions
3. **Try logging out and back in**

#### Contact Support
- **Email**: <EMAIL>
- **Response time**: Within 24 hours
- **Include**: Your phone number and issue description

### Best Practices

#### For Best Results
- **Log daily**: Consistency is key
- **Be honest**: Accurate tracking helps you improve
- **Start small**: Begin with 1-2 habits
- **Be patient**: Habit formation takes time

#### Privacy & Security
- **Your data is safe**: We follow GDPR compliance
- **No personal info shared**: Phone numbers are encrypted
- **Opt out anytime**: Type `STOP` to unsubscribe

---

## Success Tips

### Building Habits

1. **Start Small**: Begin with easy, achievable habits
2. **Be Consistent**: Track every day, even if you miss habits
3. **Stack Habits**: Link new habits to existing routines
4. **Celebrate Wins**: Acknowledge your progress and streaks
5. **Learn from Misses**: Use incomplete days as learning opportunities

### Using Lock In Effectively

1. **Set Reminders**: Use your phone's reminders to check in daily
2. **Review Weekly**: Look at your 7-day progress regularly
3. **Adjust Goals**: Modify habits if they're too easy or hard
4. **Share Progress**: Screenshot your stats to share with friends
5. **Stay Motivated**: Focus on streaks and improvement trends

---

**Need more help?** Contact <NAME_EMAIL>  
**Last Updated**: January 28, 2025
