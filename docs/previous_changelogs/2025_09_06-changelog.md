# Changelog

All notable changes to the **Lock In - WhatsApp Habit Tracker Bot** project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

# Previous Changelogs: `docs/previous_changelogs/`

---

## [2.7.0] - 2025-09-06

### Phase 1 Architecture Completion

#### Added
- Enhanced database connection pooling with monitoring and health checks
- Database pool statistics tracking and retry logic with exponential backoff
- Configurable pool settings via environment variables (DB_POOL_MAX, DB_POOL_MIN, etc.)
- Database health check endpoint at `/health?db=true`
- JWT_SECRET and admin authentication settings in `.env.example`
- DatabaseSecurity utility applied to `complianceAuditService.js` and `complianceService.js`

#### Changed
- Optimized database connection pool from basic to enterprise-grade configuration
- Updated ALL services to use DatabaseSecurity utility for SQL injection prevention
- Improved health check endpoint to include optional database status
- Updated ARCHITECTURE_AUDIT.md to reflect Phase 1 completion status
- Updated TODO.md to show completed security tasks

#### Fixed
- JWT authentication middleware properly applied to admin routes (confirmed working)
- DatabaseSecurity utility now consistently applied across ALL services
- Completed Phase 1 architectural improvements from audit

#### Removed
- Deleted 4 legacy state machine files (stateMachine.js, stateMachineCompliant.js, stateMachineWithPayments.js, stateMachinePaymentEnforced.js)
- Removed code duplication by consolidating to unifiedStateMachine.js

#### Security
- Applied DatabaseSecurity parameter validation to ALL database-accessing services
- Enhanced SQL injection protection across entire codebase
- Proper JWT authentication on all admin endpoints at `/admin/*`
- Resolved 13 of 14 security audit issues (1 deferred as infrastructure-level)

## [Unreleased]

### Documentation
- Completed comprehensive TODO Architecture documentation with all 9 layers
- Added detailed payment architecture with ThriveCart as primary provider
- Documented decision to remove FastSpring integration
- Added architecture patterns documentation (Strategy, Factory, Security Wrapper, Queue)
- Created ASCII data flow diagrams for user messages, payments, state transitions, and sessions
- Fixed missing Business Logic Layer (Layer 2) in architecture documentation
- Expanded service layer documentation to include all 11 active services
- Added complete middleware, utility, and configuration layer documentation

### Planned Changes
- Remove FastSpring integration (webhook endpoint, controller, and related code)
- Consolidate payment processing to ThriveCart only
- Migrate from `webhookController.js` to `webhookControllerCompliant.js` completely

### Testing
- Created comprehensive test suite with 82% code coverage (statements, branches, functions, lines)
- Added unit tests for all models (User, Habit, AuditLog), utilities (codeGenerator, piiHasher, motivationalQuotes, sqlIntervalBuilder), services (emailService, subscriptionService, complianceService), controllers (thrivecartController, fastspringController), and middleware (compliance, enhancedValidation)
- Implemented integration tests for webhook flows and end-to-end tests for full payment processing (ThriveCart/FastSpring webhook to email delivery)
- Updated jest.config.js with coverage thresholds (80% minimum) and HTML/LCOV reporters
- Added TESTING.md documentation for running tests, coverage reports, and best practices

---

## Version History Summary

| Version | Release Date | Key Features |
|---------|--------------|--------------|
| **2.7.0** | 2025-09-06 | Phase 1 architecture completion, DatabaseSecurity full deployment, audit resolution |
| **2.6.0** | 2025-09-05 | Architecture improvements, enhanced security, optimized DB pooling |
| **2.5.0** | 2025-01-05 | 100% security audit resolution, enhanced sessions, SQL safety |

