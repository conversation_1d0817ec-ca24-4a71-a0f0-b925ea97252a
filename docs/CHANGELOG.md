# Changelog

All notable changes to the **Lock In - WhatsApp Habit Tracker Bot** project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

# Previous Changelogs: `docs/previous_changelogs/`

---

## [2.9.0] - 2025-09-07

### Removed
- **Complete FastSpring Payment Gateway Removal**
  - Deleted FastSpring controller (`/src/controllers/fastspringController.js` - 503 lines)
  - Removed FastSpring webhook endpoint (`/webhook/fastspring`)
  - Eliminated FastSpring test files and test cases
  - Removed FastSpring environment variables (`FASTSPRING_WEBHOOK_SECRET`)
  - Cleaned up FastSpring references from payment service
  - Updated OpenAPI specification to remove FastSpring endpoints and schemas
  - Removed FastSpring from all documentation files

### Changed
- **Database Schema Updates**
  - Renamed `fastspring_order_id` to `thrivecart_order_id` in `paid_users` table
  - Renamed `fastspring_subscription_id` to `thrivecart_subscription_id` in `paid_users` table
  - Renamed `fastspring_order_id` to `provider_order_id` in `payment_transactions` table
  - Renamed `fastspring_reference` to `provider_reference` in `payment_transactions` table
  - Updated default webhook source from `fastspring` to `thrivecart`

- **Payment System Consolidation**
  - Consolidated all payment processing to ThriveCart only
  - Simplified payment configuration and webhook handling
  - Updated integration tests to use ThriveCart exclusively
  - Modified test router to support only ThriveCart webhooks

- **Documentation Updates**
  - Updated README to reflect ThriveCart-only payment processing
  - Modified all API documentation to remove FastSpring references
  - Updated legal documents (Privacy Policy, Terms of Service)
  - Cleaned up configuration guides and deployment documentation

### Added
- **Database Migration Script**
  - Created `remove_fastspring.sql` migration for safe production data migration
  - Added column renaming migrations for existing databases
  - Included audit trail for migration completion

### Fixed
- Jest configuration regex error in `testPathIgnorePatterns`
- Test suite configuration for integration and e2e tests
- Payment controller column references in database queries

---

## [2.8.0] - 2025-09-06

### Added
- **Production-Grade Docker Setup**
  - Multi-stage production Dockerfile with security hardening
  - Docker Compose production configuration with Nginx, Redis, PostgreSQL
  - Monitoring stack with Prometheus and Grafana
  - Automated backup and restore scripts for PostgreSQL
  - SSL/TLS support with self-signed certificate generator
  - Health checks for all services
  - Resource limits and reservations for containers

- **Deployment Automation**
  - Comprehensive Makefile with 30+ commands
  - Automated deployment workflows
  - Database migration management
  - Service scaling capabilities
  - Monitoring and health check commands

- **Monitoring Infrastructure**
  - Prometheus metrics collection
  - Grafana visualization dashboards
  - Node, PostgreSQL, and Redis exporters
  - Container monitoring with cAdvisor
  - Configurable alerting thresholds

### Changed
- **Project Structure Reorganization**
  - Moved all Docker configurations to `docker/` directory
  - Consolidated database files from `/database/`, `/db/` to single `src/db/`
  - Moved migrations to `src/db/migrations/`
  - Organized monitoring configs in `docker/monitoring/`
  - Restructured nginx configurations in `docker/nginx/`

- **Documentation Updates**
  - Merged and expanded deployment guide (597 lines)
  - Added complete Docker production setup instructions
  - Included step-by-step deployment procedures
  - Added troubleshooting section for common issues
  - Created Docker README explaining directory structure

### Fixed
- All failing tests now passing (100% success rate)
- Jest configuration syntax error with testPathIgnorePatterns
- Import paths updated for new database structure
- Docker Compose path references corrected

### Removed
- Unused `/memory/` directory (unimplemented orchestration system)
- Empty `/db/` directory
- Duplicate `/database/` directory
- Redundant deployment documentation in root

### Security
- Non-root user in production Docker containers
- Security headers in Nginx configuration
- Rate limiting on API endpoints
- SSL/TLS encryption enabled
- Firewall rules documentation

### Infrastructure
- Redis added for session management and caching
- Nginx reverse proxy with load balancing
- PostgreSQL with partitioning support
- Automated backup retention (30 days)
- Health endpoints for all services

---

## Version History Summary

| Version | Release Date | Key Features |
|---------|--------------|--------------|
| **2.9.0** | 2025-09-07 | Complete FastSpring removal, payment system consolidation to ThriveCart only |
| **2.8.0** | 2025-09-06 | Production Docker setup, monitoring stack, deployment automation |
| **2.7.0** | 2025-09-06 | Phase 1 architecture completion, DatabaseSecurity full deployment, audit resolution |
| **2.6.0** | 2025-09-05 | Architecture improvements, enhanced security, optimized DB pooling |
| **2.5.0** | 2025-01-05 | 100% security audit resolution, enhanced sessions, SQL safety |